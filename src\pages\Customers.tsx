import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Plus, Search, Download, Upload, FileText, Edit, Trash2 } from 'lucide-react';
import DataTable from '../components/DataTable';
import Modal from '../components/Modal';
import CustomerForm from '../components/CustomerForm';
import FilterBar from '../components/FilterBar';
import ExportImportButtons from '../components/ExportImportButtons';
import { useCustomers } from '../hooks/useDatabase';
import { exportToCSV, exportToJSON, exportToPDF, importFromCSV, importFromJSON, triggerFileImport } from '../utils/exportImport';

const Customers: React.FC = () => {
  const { isDark } = useTheme();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<Record<string, string>>({
    status: '',
    package: '',
  });

  // Use database hook
  const { customers, loading, addCustomer, updateCustomer, removeCustomer } = useCustomers();

  const columns = [
    { key: 'companyName', label: 'Company Name' },
    { key: 'picName', label: 'PIC' },
    { key: 'picTechnical', label: 'Technical PIC' },
    { key: 'email', label: 'Email' },
    { key: 'customPackage', label: 'Package' },
    { key: 'status', label: 'Status' },
    { key: 'monthlyFee', label: 'Monthly Fee' },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: ['Active', 'Suspended', 'Terminated'],
    },
    {
      key: 'package',
      label: 'Package Type',
      options: ['Startup', 'Business', 'Enterprise'],
    },
  ];

  const handleEdit = (customer: any) => {
    setEditingCustomer(customer);
    setIsModalOpen(true);
  };

  const handleDelete = (customerId: string) => {
    if (confirm('Are you sure you want to delete this customer?')) {
      try {
        removeCustomer(customerId);
        alert('Customer deleted successfully!');
      } catch (error) {
        console.error('Error deleting customer:', error);
        alert('Error deleting customer. Please try again.');
      }
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleClearFilters = () => {
    setFilters({ status: '', package: '' });
  };

  const handleImport = async () => {
    try {
      const file = await triggerFileImport('.csv,.json');
      let importedData;

      if (file.name.endsWith('.csv')) {
        importedData = await importFromCSV(file);
      } else if (file.name.endsWith('.json')) {
        importedData = await importFromJSON(file);
      } else {
        throw new Error('Unsupported file format');
      }

      if (window.confirm(`Import ${importedData.length} customers? This will add new customers to the existing list.`)) {
        importedData.forEach(customerData => {
          addCustomer(customerData);
        });
        alert(`Successfully imported ${importedData.length} customers!`);
      }
    } catch (error) {
      console.error('Error importing file:', error);
      alert('Error importing file. Please check the file format.');
    }
  };

  const handleExportExcel = () => {
    exportToCSV(customers, 'customers');
  };

  const handleExportPDF = () => {
    exportToPDF(customers, 'customers', 'Customer List');
  };

  const handleSave = (customerData: any) => {
    try {
      if (editingCustomer) {
        updateCustomer(editingCustomer.id, customerData);
        alert('Customer updated successfully!');
      } else {
        addCustomer(customerData);
        alert('Customer added successfully!');
      }
      setIsModalOpen(false);
      setEditingCustomer(null);
    } catch (error) {
      console.error('Error saving customer:', error);
      alert('Error saving customer. Please try again.');
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setEditingCustomer(null);
  };

  const actions = [
    {
      icon: Edit,
      label: 'Edit',
      onClick: handleEdit,
      className: 'text-blue-400 hover:text-blue-300',
    },
    {
      icon: Trash2,
      label: 'Delete',
      onClick: (item: any) => handleDelete(item.id),
      className: 'text-red-400 hover:text-red-300',
    },
  ];

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.picName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !filters.status || customer.status === filters.status;
    const matchesPackage = !filters.package || customer.customPackage.toLowerCase().includes(filters.package.toLowerCase());
    
    return matchesSearch && matchesStatus && matchesPackage;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className={`text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>Customer Management</h1>
          <p className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            Manage your ISP customers and their service details
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <ExportImportButtons
            onImport={handleImport}
            onExportExcel={handleExportExcel}
            onExportPDF={handleExportPDF}
          />
          <button
            onClick={() => setIsModalOpen(true)}
            className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Customer
          </button>
        </div>
      </div>

      <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border`}>
        <div className={`p-6 border-b ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="relative">
            <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${isDark ? 'text-gray-400' : 'text-gray-500'} h-4 w-4`} />
            <input
              type="text"
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'} border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
            />
          </div>
        </div>

        <FilterBar
          filters={filterOptions}
          activeFilters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
        />

        <DataTable
          columns={columns}
          data={filteredCustomers}
          actions={actions}
          emptyMessage="No customers found"
        />
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={handleCancel}
        title={editingCustomer ? 'Edit Customer' : 'Add New Customer'}
        size="lg"
      >
        <CustomerForm
          customer={editingCustomer}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </Modal>
    </div>
  );
};

export default Customers;