import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Plus, Search, Upload, FileText, Edit, Trash2, Mail } from 'lucide-react';
import DataTable from '../components/DataTable';
import Modal from '../components/Modal';
import CustomerForm from '../components/CustomerForm';
import FilterBar from '../components/FilterBar';
import ExportImportButtons from '../components/ExportImportButtons';
import { useCustomers } from '../hooks/useDatabase';
import { exportToCSV, exportToJSON, exportToPDF, importFromCSV, importFromJSON, triggerFileImport } from '../utils/exportImport';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

const Customers: React.FC = () => {
  const { isDark } = useTheme();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<Record<string, string>>({
    status: '',
    package: '',
    monthlyFee: '',
    city: '',
  });

  // Use database hook
  const { customers, loading, addCustomer, updateCustomer, removeCustomer } = useCustomers();

  const columns = [
    { key: 'companyName', label: 'Company Name' },
    { key: 'picName', label: 'PIC' },
    { key: 'picTechnical', label: 'Technical PIC' },
    { key: 'email', label: 'Email' },
    { key: 'customPackage', label: 'Package' },
    { key: 'status', label: 'Status' },
    { key: 'monthlyFee', label: 'Monthly Fee' },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: ['Active', 'Suspended', 'Terminated', 'Pending', 'Trial'],
    },
    {
      key: 'package',
      label: 'Package Type',
      options: ['Startup', 'Business', 'Enterprise', 'Premium', 'Basic'],
    },
    {
      key: 'monthlyFee',
      label: 'Fee Range',
      options: ['< 1M', '1M - 5M', '5M - 10M', '> 10M'],
    },
    {
      key: 'city',
      label: 'City',
      options: ['Jakarta', 'Surabaya', 'Bandung', 'Medan', 'Semarang'],
    },
  ];

  const handleEdit = (customer: any) => {
    setEditingCustomer(customer);
    setIsModalOpen(true);
  };

  const handleDelete = (customerId: string) => {
    if (confirm('Are you sure you want to delete this customer?')) {
      try {
        removeCustomer(customerId);
        alert('Customer deleted successfully!');
      } catch (error) {
        console.error('Error deleting customer:', error);
        alert('Error deleting customer. Please try again.');
      }
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleClearFilters = () => {
    setFilters({ status: '', package: '', monthlyFee: '', city: '' });
  };

  const handleImport = async () => {
    try {
      const file = await triggerFileImport('.csv,.json');
      let importedData;

      if (file.name.endsWith('.csv')) {
        importedData = await importFromCSV(file);
      } else if (file.name.endsWith('.json')) {
        importedData = await importFromJSON(file);
      } else {
        throw new Error('Unsupported file format');
      }

      if (window.confirm(`Import ${importedData.length} customers? This will add new customers to the existing list.`)) {
        importedData.forEach(customerData => {
          addCustomer(customerData);
        });
        alert(`Successfully imported ${importedData.length} customers!`);
      }
    } catch (error) {
      console.error('Error importing file:', error);
      alert('Error importing file. Please check the file format.');
    }
  };

  const handleExportExcel = () => {
    exportToCSV(customers, 'customers');
  };

  const handleExportPDF = () => {
    exportToPDF(customers, 'customers', 'Customer List');
  };

  const handlePrintCustomerDetail = (customer: any) => {
    const doc = new jsPDF();
    
    // Header with company logo area
    doc.setFontSize(24);
    doc.setTextColor(41, 128, 185);
    doc.text('CRM WIZNET', 20, 25);
    
    doc.setFontSize(18);
    doc.setTextColor(40, 40, 40);
    doc.text('Customer Detail Report', 20, 40);
    
    // Add line separator
    doc.setLineWidth(0.5);
    doc.setDrawColor(200, 200, 200);
    doc.line(20, 45, 190, 45);
    
    // Customer info
    doc.setFontSize(12);
    doc.setTextColor(60, 60, 60);
    
    const details = [
      ['Company Name', customer.companyName || 'N/A'],
      ['PIC Name', customer.picName || 'N/A'],
      ['PIC Phone', customer.picPhone || 'N/A'],
      ['Technical PIC', customer.picTechnical || 'N/A'],
      ['Technical PIC Phone', customer.picTechnicalPhone || 'N/A'],
      ['Technical PIC Email', customer.picTechnicalEmail || 'N/A'],
      ['Email', customer.email || 'N/A'],
      ['Phone', customer.phone || 'N/A'],
      ['Address', customer.address || 'N/A'],
      ['Status', customer.status || 'N/A'],
      ['Package', customer.customPackage || 'N/A'],
      ['Monthly Fee', customer.monthlyFee || 'N/A'],
      ['Notes', customer.notes || 'No additional notes'],
      ['Created Date', customer.createdAt ? new Date(customer.createdAt).toLocaleDateString('id-ID') : 'N/A'],
      ['Last Updated', customer.updatedAt ? new Date(customer.updatedAt).toLocaleDateString('id-ID') : 'N/A']
    ];
    
    (doc as any).autoTable({
      startY: 55,
      head: [['Field', 'Information']],
      body: details,
      theme: 'striped',
      headStyles: { 
        fillColor: [41, 128, 185],
        textColor: [255, 255, 255],
        fontSize: 12,
        fontStyle: 'bold'
      },
      styles: { 
        fontSize: 10,
        cellPadding: 5
      },
      columnStyles: {
        0: { 
          fontStyle: 'bold', 
          cellWidth: 50,
          fillColor: [245, 245, 245]
        },
        1: { 
          cellWidth: 120,
          textColor: [60, 60, 60]
        }
      },
      alternateRowStyles: {
        fillColor: [250, 250, 250]
      }
    });
    
    // Footer with timestamp and page number
    const pageCount = (doc as any).internal.getNumberOfPages();
    const pageHeight = doc.internal.pageSize.height;
    
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128);
    doc.text(`Generated on ${new Date().toLocaleDateString('id-ID')} at ${new Date().toLocaleTimeString('id-ID')}`, 20, pageHeight - 15);
    doc.text(`Page ${pageCount}`, 170, pageHeight - 15);
    
    // Add border
    doc.setLineWidth(0.5);
    doc.setDrawColor(200, 200, 200);
    doc.rect(15, 15, 180, pageHeight - 30);
    
    doc.save(`customer-detail-${customer.companyName.replace(/\s+/g, '-').toLowerCase()}.pdf`);
    
    // Show success message
    alert(`PDF berhasil diunduh untuk ${customer.companyName}`);
  };



  const handleSendEmail = (customer: any) => {
    const subject = encodeURIComponent(`Informasi Akun ISP - ${customer.companyName}`);
    const body = encodeURIComponent(
      `Kepada Yth. ${customer.picName || 'Bapak/Ibu'},\n\n` +
      `Dengan hormat,\n\n` +
      `Berikut adalah informasi terkini mengenai layanan internet Anda:\n\n` +
      `=== INFORMASI PELANGGAN ===\n` +
      `Nama Perusahaan: ${customer.companyName || 'N/A'}\n` +
      `PIC: ${customer.picName || 'N/A'}\n` +
      `Email: ${customer.email || 'N/A'}\n` +
      `Telepon: ${customer.phone || 'N/A'}\n` +
      `Alamat: ${customer.address || 'N/A'}\n\n` +
      `=== INFORMASI LAYANAN ===\n` +
      `Paket Berlangganan: ${customer.customPackage || 'N/A'}\n` +
      `Status Layanan: ${customer.status || 'N/A'}\n` +
      `Biaya Bulanan: ${customer.monthlyFee || 'N/A'}\n\n` +
      `=== KONTAK TEKNIS ===\n` +
      `PIC Teknis: ${customer.picTechnical || 'Belum ditentukan'}\n` +
      `Telepon Teknis: ${customer.picTechnicalPhone || 'Belum ditentukan'}\n` +
      `Email Teknis: ${customer.picTechnicalEmail || 'Belum ditentukan'}\n\n` +
      `Catatan: ${customer.notes || 'Tidak ada catatan khusus'}\n\n` +
      `Jika ada pertanyaan atau memerlukan bantuan teknis, silakan hubungi tim support kami.\n\n` +
      `Terima kasih atas kepercayaan Anda menggunakan layanan kami.\n\n` +
      `Hormat kami,\n` +
      `Tim CRM Wiznet\n` +
      `Email: <EMAIL>\n` +
      `Telepon: 021-1234-5678\n` +
      `Website: www.crmwiznet.com`
    );
    
    // Open email client
    window.open(`mailto:${customer.email}?subject=${subject}&body=${body}`);
    
    // Show confirmation
    alert(`Email template telah dibuka untuk ${customer.companyName}. Silakan review dan kirim email melalui aplikasi email Anda.`);
  };

  const handleSave = (customerData: any) => {
    try {
      if (editingCustomer) {
        updateCustomer(editingCustomer.id, customerData);
        alert('Customer updated successfully!');
      } else {
        addCustomer(customerData);
        alert('Customer added successfully!');
      }
      setIsModalOpen(false);
      setEditingCustomer(null);
    } catch (error) {
      console.error('Error saving customer:', error);
      alert('Error saving customer. Please try again.');
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setEditingCustomer(null);
  };

  const actions = [
    {
      icon: FileText,
      label: 'Print PDF',
      onClick: handlePrintCustomerDetail,
      className: isDark ? 'text-purple-400 hover:text-purple-300' : 'text-purple-600 hover:text-purple-500',
    },
    {
      icon: Mail,
      label: 'Send Email',
      onClick: handleSendEmail,
      className: isDark ? 'text-yellow-400 hover:text-yellow-300' : 'text-yellow-600 hover:text-yellow-500',
    },
    {
      icon: Edit,
      label: 'Edit',
      onClick: handleEdit,
      className: isDark ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-500',
    },
    {
      icon: Trash2,
      label: 'Delete',
      onClick: (item: any) => handleDelete(item.id),
      className: isDark ? 'text-red-400 hover:text-red-300' : 'text-red-600 hover:text-red-500',
    },
  ];

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.picName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !filters.status || customer.status === filters.status;
    const matchesPackage = !filters.package || customer.customPackage.toLowerCase().includes(filters.package.toLowerCase());
    
    // Fee range filter
    const matchesFeeRange = !filters.monthlyFee || (() => {
      const fee = parseInt(customer.monthlyFee?.replace(/[^0-9]/g, '') || '0');
      switch (filters.monthlyFee) {
        case '< 1M': return fee < 1000000;
        case '1M - 5M': return fee >= 1000000 && fee <= 5000000;
        case '5M - 10M': return fee > 5000000 && fee <= 10000000;
        case '> 10M': return fee > 10000000;
        default: return true;
      }
    })();
    
    // City filter
    const matchesCity = !filters.city || customer.address?.toLowerCase().includes(filters.city.toLowerCase());
    
    return matchesSearch && matchesStatus && matchesPackage && matchesFeeRange && matchesCity;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className={`text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>Customer Management</h1>
          <p className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            Manage your ISP customers and their service details
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <ExportImportButtons
            onImport={handleImport}
            onExportExcel={handleExportExcel}
            onExportPDF={handleExportPDF}
          />
          <button
            onClick={() => setIsModalOpen(true)}
            className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Customer
          </button>
        </div>
      </div>

      <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border`}>
        <div className={`p-6 border-b ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="relative">
            <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${isDark ? 'text-gray-400' : 'text-gray-500'} h-4 w-4`} />
            <input
              type="text"
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'} border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
            />
          </div>
        </div>

        <FilterBar
          filters={filterOptions}
          activeFilters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
        />

        <DataTable
          columns={columns}
          data={filteredCustomers}
          actions={actions}
          emptyMessage="No customers found"
        />
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={handleCancel}
        title={editingCustomer ? 'Edit Customer' : 'Add New Customer'}
        size="lg"
      >
        <CustomerForm
          customer={editingCustomer}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </Modal>
    </div>
  );
};

export default Customers;