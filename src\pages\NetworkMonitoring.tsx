import React from 'react';
import { Activity, Server, Wifi, <PERSON><PERSON><PERSON>riangle, CheckCircle, XCircle } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

const NetworkMonitoring: React.FC = () => {
  const { isDark } = useTheme();
  const networkNodes = [
    {
      id: 'NODE-001',
      name: 'Jakarta Core Router',
      location: 'Jakarta Data Center',
      status: 'online',
      uptime: '99.9%',
      bandwidth: '85%',
      latency: '12ms',
      packetsLoss: '0.01%',
    },
    {
      id: 'NODE-002',
      name: 'Surabaya Edge Router',
      location: 'Surabaya Data Center',
      status: 'online',
      uptime: '99.7%',
      bandwidth: '72%',
      latency: '18ms',
      packetsLoss: '0.02%',
    },
    {
      id: 'NODE-003',
      name: 'Bandung Access Point',
      location: 'Bandung Tower',
      status: 'warning',
      uptime: '98.2%',
      bandwidth: '93%',
      latency: '25ms',
      packetsLoss: '0.15%',
    },
    {
      id: 'NODE-004',
      name: 'Backup Server',
      location: 'Jakarta Data Center',
      status: 'offline',
      uptime: '0%',
      bandwidth: '0%',
      latency: 'N/A',
      packetsLoss: 'N/A',
    },
  ];

  const alerts = [
    {
      id: 1,
      severity: 'critical',
      message: 'Backup Server is offline',
      timestamp: '2024-01-21 14:30:00',
      node: 'Backup Server',
    },
    {
      id: 2,
      severity: 'warning',
      message: 'High bandwidth usage detected',
      timestamp: '2024-01-21 13:45:00',
      node: 'Bandung Access Point',
    },
    {
      id: 3,
      severity: 'info',
      message: 'Scheduled maintenance completed',
      timestamp: '2024-01-21 10:00:00',
      node: 'Jakarta Core Router',
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
      case 'offline':
        return <XCircle className="h-5 w-5 text-red-400" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return isDark ? 'bg-green-900 text-green-300' : 'bg-green-100 text-green-800';
      case 'warning':
        return isDark ? 'bg-yellow-900 text-yellow-300' : 'bg-yellow-100 text-yellow-800';
      case 'offline':
        return isDark ? 'bg-red-900 text-red-300' : 'bg-red-100 text-red-800';
      default:
        return isDark ? 'bg-gray-900 text-gray-300' : 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return isDark ? 'bg-red-900 text-red-300' : 'bg-red-100 text-red-800';
      case 'warning':
        return isDark ? 'bg-yellow-900 text-yellow-300' : 'bg-yellow-100 text-yellow-800';
      case 'info':
        return isDark ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-800';
      default:
        return isDark ? 'bg-gray-900 text-gray-300' : 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Network Monitoring</h1>
          <p className="mt-1 text-sm text-gray-400">
            Real-time monitoring of network infrastructure and performance
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700">
            <Activity className="mr-2 h-4 w-4" />
            Refresh Status
          </button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <CheckCircle className="h-6 w-6 text-green-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">
                {networkNodes.filter(n => n.status === 'online').length}
              </div>
              <div className="text-sm text-gray-400">Online Nodes</div>
            </div>
          </div>
        </div>
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <AlertTriangle className="h-6 w-6 text-yellow-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">
                {networkNodes.filter(n => n.status === 'warning').length}
              </div>
              <div className="text-sm text-gray-400">Warning Status</div>
            </div>
          </div>
        </div>
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <XCircle className="h-6 w-6 text-red-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">
                {networkNodes.filter(n => n.status === 'offline').length}
              </div>
              <div className="text-sm text-gray-400">Offline Nodes</div>
            </div>
          </div>
        </div>
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <Activity className="h-6 w-6 text-blue-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">99.2%</div>
              <div className="text-sm text-gray-400">Overall Uptime</div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Network Nodes */}
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-6">
            <h3 className="text-lg font-medium text-white mb-4">Network Nodes Status</h3>
            <div className="space-y-4">
              {networkNodes.map((node) => (
                <div key={node.id} className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      {getStatusIcon(node.status)}
                      <div className="ml-3">
                        <div className="text-sm font-medium text-white">{node.name}</div>
                        <div className="text-sm text-gray-400">{node.location}</div>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(node.status)}`}>
                      {node.status}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-400">Uptime</div>
                      <div className="text-white font-medium">{node.uptime}</div>
                    </div>
                    <div>
                      <div className="text-gray-400">Bandwidth</div>
                      <div className="text-white font-medium">{node.bandwidth}</div>
                    </div>
                    <div>
                      <div className="text-gray-400">Latency</div>
                      <div className="text-white font-medium">{node.latency}</div>
                    </div>
                    <div>
                      <div className="text-gray-400">Packet Loss</div>
                      <div className="text-white font-medium">{node.packetsLoss}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Alerts */}
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-6">
            <h3 className="text-lg font-medium text-white mb-4">Recent Alerts</h3>
            <div className="space-y-4">
              {alerts.map((alert) => (
                <div key={alert.id} className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                          {alert.severity}
                        </span>
                        <span className="ml-2 text-sm text-gray-400">{alert.node}</span>
                      </div>
                      <p className="text-sm text-white">{alert.message}</p>
                      <p className="text-xs text-gray-400 mt-1">{alert.timestamp}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkMonitoring;