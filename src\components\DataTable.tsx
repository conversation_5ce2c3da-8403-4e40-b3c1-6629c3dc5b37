import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { DivideIcon as LucideIcon } from 'lucide-react';

interface Column {
  key: string;
  label: string;
}

interface Action {
  icon: LucideIcon;
  label: string;
  onClick: (item: any) => void;
  className?: string;
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  actions?: Action[];
  emptyMessage?: string;
}

const DataTable: React.FC<DataTableProps> = ({
  columns,
  data,
  actions,
  emptyMessage = 'No data available',
}) => {
  const { isDark } = useTheme();

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      Active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      Suspended: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      Pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      Won: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      Lost: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      Open: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      Closed: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
      'In Progress': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        statusClasses[status as keyof typeof statusClasses] || statusClasses.Pending
      }`}>
        {status}
      </span>
    );
  };

  if (data.length === 0) {
    return (
      <div className={`p-8 text-center ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
        {emptyMessage}
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className={`min-w-full divide-y ${isDark ? 'divide-gray-700' : 'divide-gray-200'}`}>
        <thead className={isDark ? 'bg-gray-750' : 'bg-gray-50'}>
          <tr>
            {columns.map((column) => (
              <th
                key={column.key}
                className={`px-6 py-3 text-left text-xs font-medium ${isDark ? 'text-gray-400' : 'text-gray-500'} uppercase tracking-wider`}
              >
                {column.label}
              </th>
            ))}
            {actions && actions.length > 0 && (
              <th className={`px-6 py-3 text-right text-xs font-medium ${isDark ? 'text-gray-400' : 'text-gray-500'} uppercase tracking-wider`}>
                Actions
              </th>
            )}
          </tr>
        </thead>
        <tbody className={`divide-y ${isDark ? 'divide-gray-700' : 'divide-gray-200'}`}>
          {data.map((item, index) => (
            <tr key={index} className={`${isDark ? 'hover:bg-gray-750' : 'hover:bg-gray-50'} transition-colors`}>
              {columns.map((column) => (
                <td key={column.key} className="px-6 py-4 whitespace-nowrap">
                  {column.key === 'status' ? (
                    getStatusBadge(item[column.key])
                  ) : (
                    <div className={`text-sm ${isDark ? 'text-white' : 'text-gray-900'}`}>{item[column.key]}</div>
                  )}
                </td>
              ))}
              {actions && actions.length > 0 && (
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    {actions.map((action, actionIndex) => (
                      <button
                        key={actionIndex}
                        onClick={() => action.onClick(item)}
                        className={`p-1 rounded ${isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-100'} transition-colors ${action.className || (isDark ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900')}`}
                        title={action.label}
                      >
                        <action.icon className="h-4 w-4" />
                      </button>
                    ))}
                  </div>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DataTable;