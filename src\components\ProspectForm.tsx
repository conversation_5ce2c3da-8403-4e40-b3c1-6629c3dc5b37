import React, { useState, useEffect } from 'react';

interface ProspectFormProps {
  prospect?: any;
  onSave: (data: any) => void;
  onCancel: () => void;
}

const ProspectForm: React.FC<ProspectFormProps> = ({ prospect, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    companyName: '',
    contactPerson: '',
    email: '',
    phone: '',
    source: '',
    status: 'New',
    estimatedValue: '',
    followUpDate: '',
    notes: '',
  });

  const sources = [
    'Website',
    'Referral',
    'Cold Call',
    'Email Campaign',
    'Social Media',
    'Trade Show',
    'Other',
  ];

  const statuses = [
    'New',
    'Contacted',
    'Qualified',
    'Proposal Sent',
    'Negotiation',
    'Won',
    'Lost',
  ];

  useEffect(() => {
    if (prospect) {
      setFormData({
        companyName: prospect.companyName || '',
        contactPerson: prospect.contactPerson || '',
        email: prospect.email || '',
        phone: prospect.phone || '',
        source: prospect.source || '',
        status: prospect.status || 'New',
        estimatedValue: prospect.estimatedValue || '',
        followUpDate: prospect.followUpDate || '',
        notes: prospect.notes || '',
      });
    }
  }, [prospect]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label htmlFor="companyName" className="block text-sm font-medium text-gray-300">
            Company Name *
          </label>
          <input
            type="text"
            id="companyName"
            name="companyName"
            required
            value={formData.companyName}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500"
          />
        </div>
        
        <div>
          <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-300">
            Contact Person *
          </label>
          <input
            type="text"
            id="contactPerson"
            name="contactPerson"
            required
            value={formData.contactPerson}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500"
          />
        </div>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-300">
            Email *
          </label>
          <input
            type="email"
            id="email"
            name="email"
            required
            value={formData.email}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500"
          />
        </div>
        
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-300">
            Phone *
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            required
            value={formData.phone}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500"
          />
        </div>
        
        <div>
          <label htmlFor="source" className="block text-sm font-medium text-gray-300">
            Source *
          </label>
          <select
            id="source"
            name="source"
            required
            value={formData.source}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-purple-500 focus:ring-purple-500"
          >
            <option value="">Select Source</option>
            {sources.map((source) => (
              <option key={source} value={source}>{source}</option>
            ))}
          </select>
        </div>
        
        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-300">
            Status *
          </label>
          <select
            id="status"
            name="status"
            required
            value={formData.status}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-purple-500 focus:ring-purple-500"
          >
            {statuses.map((status) => (
              <option key={status} value={status}>{status}</option>
            ))}
          </select>
        </div>
        
        <div>
          <label htmlFor="estimatedValue" className="block text-sm font-medium text-gray-300">
            Estimated Value
          </label>
          <input
            type="text"
            id="estimatedValue"
            name="estimatedValue"
            placeholder="Rp 5,000,000"
            value={formData.estimatedValue}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500"
          />
        </div>
        
        <div>
          <label htmlFor="followUpDate" className="block text-sm font-medium text-gray-300">
            Follow Up Date
          </label>
          <input
            type="date"
            id="followUpDate"
            name="followUpDate"
            value={formData.followUpDate}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-purple-500 focus:ring-purple-500"
          />
        </div>
      </div>
      
      <div>
        <label htmlFor="notes" className="block text-sm font-medium text-gray-300">
          Notes
        </label>
        <textarea
          id="notes"
          name="notes"
          rows={3}
          value={formData.notes}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500"
        />
      </div>
      
      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-700 border border-gray-600 rounded-md hover:bg-gray-600"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700"
        >
          {prospect ? 'Update' : 'Create'} Prospect
        </button>
      </div>
    </form>
  );
};

export default ProspectForm;