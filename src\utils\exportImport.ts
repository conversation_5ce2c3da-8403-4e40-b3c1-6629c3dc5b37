// Export/Import utilities for CRM data
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

export const exportToCSV = (data: any[], filename: string) => {
  if (!data || data.length === 0) {
    alert('No data to export');
    return;
  }

  // Get headers from the first object
  const headers = Object.keys(data[0]);
  
  // Create CSV content
  const csvContent = [
    headers.join(','), // Header row
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        // Escape commas and quotes in values
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      }).join(',')
    )
  ].join('\n');

  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const exportToXLS = (data: any[], filename: string) => {
  if (!data || data.length === 0) {
    alert('No data to export');
    return;
  }

  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Convert data to worksheet
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Auto-size columns
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    const colWidths: any[] = [];

    for (let C = range.s.c; C <= range.e.c; ++C) {
      let maxWidth = 10;
      for (let R = range.s.r; R <= range.e.r; ++R) {
        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
        const cell = worksheet[cellAddress];
        if (cell && cell.v) {
          const cellLength = cell.v.toString().length;
          if (cellLength > maxWidth) {
            maxWidth = cellLength;
          }
        }
      }
      colWidths[C] = { width: Math.min(maxWidth + 2, 50) };
    }
    worksheet['!cols'] = colWidths;

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

    // Write the file
    XLSX.writeFile(workbook, `${filename}.xlsx`);

    console.log(`Successfully exported ${data.length} records to ${filename}.xlsx`);
  } catch (error) {
    console.error('Error exporting to XLS:', error);
    alert('Error exporting to Excel. Please try again.');
  }
};

export const exportToJSON = (data: any[], filename: string) => {
  if (!data || data.length === 0) {
    alert('No data to export');
    return;
  }

  const jsonContent = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.json`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const exportToPDF = async (data: any[], filename: string, title: string) => {
  if (!data || data.length === 0) {
    alert('No data to export');
    return;
  }

  try {
    // Create new PDF document in landscape mode for better table layout
    const doc = new jsPDF('landscape', 'mm', 'a4');

    // Add title
    doc.setFontSize(18);
    doc.setTextColor(124, 58, 237); // Purple color
    doc.text(title, 20, 20);

    // Add date and summary
    doc.setFontSize(10);
    doc.setTextColor(100, 100, 100);
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 30);
    doc.text(`Total Records: ${data.length}`, 20, 36);

    // Define specific columns for different data types
    let headers: string[] = [];
    let tableData: any[][] = [];

    // Check if this is customer data based on the presence of specific fields
    if (data[0] && 'companyName' in data[0]) {
      // Customer-specific columns
      headers = ['Company Name', 'PIC Name', 'Email', 'Phone', 'Package', 'Status', 'Monthly Fee', 'Address'];
      tableData = data.map(row => [
        row.companyName || '',
        row.picName || '',
        row.email || '',
        row.phone || '',
        row.customPackage || row.package || '',
        row.status || '',
        row.monthlyFee ? `Rp ${row.monthlyFee}` : '',
        row.address || ''
      ]);
    } else if (data[0] && 'subject' in data[0]) {
      // Ticket-specific columns
      headers = ['ID', 'Customer', 'Subject', 'Priority', 'Status', 'Category', 'Created Date'];
      tableData = data.map(row => [
        row.id || '',
        row.customer || '',
        row.subject || '',
        row.priority || '',
        row.status || '',
        row.category || '',
        row.createdDate || ''
      ]);
    } else if (data[0] && 'contactPerson' in data[0]) {
      // Prospect-specific columns
      headers = ['Company Name', 'Contact Person', 'Email', 'Phone', 'Status', 'Source', 'Package Interest', 'Est. Value'];
      tableData = data.map(row => [
        row.companyName || '',
        row.contactPerson || '',
        row.email || '',
        row.phone || '',
        row.status || '',
        row.source || '',
        row.interestedPackage || '',
        row.estimatedValue || ''
      ]);
    } else {
      // Generic fallback - use all fields
      headers = Object.keys(data[0]);
      tableData = data.map(row => headers.map(header => row[header] || ''));
    }

    // Add table with improved styling
    autoTable(doc, {
      head: [headers],
      body: tableData,
      startY: 45,
      theme: 'striped',
      headStyles: {
        fillColor: [124, 58, 237], // Purple header
        textColor: [255, 255, 255],
        fontStyle: 'bold',
        fontSize: 9,
        halign: 'center'
      },
      bodyStyles: {
        textColor: [50, 50, 50],
        fontSize: 8,
        cellPadding: { top: 4, right: 3, bottom: 4, left: 3 }
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      columnStyles: {
        0: { cellWidth: 'auto', minCellWidth: 25 }, // Company/ID
        1: { cellWidth: 'auto', minCellWidth: 20 }, // PIC/Customer
        2: { cellWidth: 'auto', minCellWidth: 30 }, // Email/Subject
        3: { cellWidth: 'auto', minCellWidth: 20 }, // Phone
        4: { cellWidth: 'auto', minCellWidth: 20 }, // Package/Priority
        5: { cellWidth: 'auto', minCellWidth: 15 }, // Status
        6: { cellWidth: 'auto', minCellWidth: 20 }, // Monthly Fee/Category
        7: { cellWidth: 'auto', minCellWidth: 30 }  // Address/Date
      },
      margin: { top: 45, left: 15, right: 15, bottom: 20 },
      styles: {
        overflow: 'linebreak',
        cellWidth: 'wrap'
      },
      didDrawPage: function (data) {
        // Add page numbers
        const pageCount = doc.getNumberOfPages();
        doc.setFontSize(8);
        doc.setTextColor(128, 128, 128);
        doc.text(
          `Page ${data.pageNumber} of ${pageCount}`,
          doc.internal.pageSize.width - 30,
          doc.internal.pageSize.height - 10
        );
      }
    });

    // Save the PDF
    doc.save(`${filename}.pdf`);

    console.log(`Successfully exported ${data.length} records to ${filename}.pdf`);
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    alert('Error exporting to PDF. Please try again.');
  }
};

export const importFromCSV = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string;
        const lines = csv.split('\n');
        const headers = lines[0].split(',').map(h => h.trim());
        
        const data = lines.slice(1)
          .filter(line => line.trim()) // Remove empty lines
          .map(line => {
            const values = line.split(',').map(v => v.trim().replace(/^"|"$/g, '')); // Remove quotes
            const obj: any = {};
            headers.forEach((header, index) => {
              obj[header] = values[index] || '';
            });
            return obj;
          });
        
        resolve(data);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => reject(new Error('Error reading file'));
    reader.readAsText(file);
  });
};

export const importFromXLS = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });

        // Get the first worksheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        resolve(jsonData);
      } catch (error) {
        reject(new Error('Error reading Excel file. Please ensure it\'s a valid .xlsx file.'));
      }
    };

    reader.onerror = () => reject(new Error('Error reading file'));
    reader.readAsArrayBuffer(file);
  });
};

export const importFromJSON = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const json = e.target?.result as string;
        const data = JSON.parse(json);
        
        if (!Array.isArray(data)) {
          reject(new Error('JSON file must contain an array of objects'));
          return;
        }
        
        resolve(data);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => reject(new Error('Error reading file'));
    reader.readAsText(file);
  });
};

// Email utilities
export const sendEmail = (to: string, subject: string, body: string, attachments?: File[]) => {
  try {
    // Create mailto link with proper encoding
    const mailtoLink = `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

    // Create a temporary link element
    const link = document.createElement('a');
    link.href = mailtoLink;
    link.style.display = 'none';
    document.body.appendChild(link);

    // Click the link to open email client
    link.click();

    // Clean up
    document.body.removeChild(link);

    // Show success message
    setTimeout(() => {
      alert(`✅ Email client opened successfully!\n\nTo: ${to}\nSubject: ${subject}\n\nPlease complete sending the email from your email client.`);
    }, 500);

    return true;
  } catch (error) {
    console.error('Error opening email client:', error);
    alert('❌ Error opening email client. Please check your email configuration.');
    return false;
  }
};

export const sendBulkEmail = (recipients: string[], subject: string, body: string) => {
  const recipientList = recipients.join(';');
  sendEmail(recipientList, subject, body);
};

// RFO Email template
export const sendRFOEmail = (customerEmails: string[], rfoContent: string, ticketId: string) => {
  const subject = `Service Update - Ticket ${ticketId}`;
  const body = `${rfoContent}\n\nTicket Reference: ${ticketId}\n\nFor any questions, please contact our support team.`;
  
  sendBulkEmail(customerEmails, subject, body);
};

// Invoice Email template
export const sendInvoiceEmail = (customerEmail: string, invoiceData: any) => {
  const subject = `Invoice ${invoiceData.id} - ${invoiceData.customerName}`;
  const body = `Dear ${invoiceData.customerName},

Please find attached your invoice details:

Invoice ID: ${invoiceData.id}
Issue Date: ${invoiceData.issueDate}
Due Date: ${invoiceData.dueDate}
Amount: ${invoiceData.amount}

Description: ${invoiceData.description || 'Monthly Service Fee'}

Please ensure payment is made by the due date to avoid any service interruption.

Thank you for your business.

Best regards,
Billing Department`;

  sendEmail(customerEmail, subject, body);
};

// Utility to trigger file input for import
export const triggerFileImport = (accept: string = '.csv,.json'): Promise<File> => {
  return new Promise((resolve, reject) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = accept;
    input.style.display = 'none';
    
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        resolve(file);
      } else {
        reject(new Error('No file selected'));
      }
    };
    
    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
  });
};

// Generate comprehensive report with multiple sheets/sections
export const generateComprehensiveReport = async (
  data: {
    customers: any[];
    prospects: any[];
    tickets: any[];
    invoices: any[];
  },
  format: 'pdf' | 'excel' = 'pdf'
) => {
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = `CRM_Comprehensive_Report_${timestamp}`;

  if (format === 'excel') {
    try {
      // Create a new workbook
      const workbook = XLSX.utils.book_new();

      // Summary sheet
      const summaryData = [
        ['CRM Comprehensive Report'],
        ['Generated on:', new Date().toLocaleDateString()],
        [''],
        ['Summary Statistics:'],
        ['Total Customers:', data.customers.length],
        ['Total Prospects:', data.prospects.length],
        ['Total Tickets:', data.tickets.length],
        ['Total Invoices:', data.invoices.length],
        [''],
        ['Customer Status Breakdown:'],
        ['Active:', data.customers.filter(c => c.status === 'Active').length],
        ['Suspended:', data.customers.filter(c => c.status === 'Suspended').length],
        ['Terminated:', data.customers.filter(c => c.status === 'Terminated').length],
        [''],
        ['Ticket Status Breakdown:'],
        ['Open:', data.tickets.filter(t => t.status === 'Open').length],
        ['In Progress:', data.tickets.filter(t => t.status === 'In Progress').length],
        ['Resolved:', data.tickets.filter(t => t.status === 'Resolved').length],
        ['Closed:', data.tickets.filter(t => t.status === 'Closed').length],
      ];
      const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summaryWs, 'Summary');

      // Customers sheet
      if (data.customers.length > 0) {
        const customerData = data.customers.map(c => ({
          'Company Name': c.companyName,
          'PIC Name': c.picName,
          'Email': c.email,
          'Phone': c.phone,
          'Package': c.customPackage,
          'Status': c.status,
          'Monthly Fee': c.monthlyFee,
          'Address': c.address,
          'Created Date': c.createdAt
        }));
        const customerWs = XLSX.utils.json_to_sheet(customerData);
        XLSX.utils.book_append_sheet(workbook, customerWs, 'Customers');
      }

      // Prospects sheet
      if (data.prospects.length > 0) {
        const prospectData = data.prospects.map(p => ({
          'Company Name': p.companyName,
          'Contact Person': p.contactPerson,
          'Email': p.email,
          'Phone': p.phone,
          'Status': p.status,
          'Source': p.source,
          'Interested Package': p.interestedPackage,
          'Estimated Value': p.estimatedValue,
          'Follow Up Date': p.followUpDate,
          'Created Date': p.createdAt
        }));
        const prospectWs = XLSX.utils.json_to_sheet(prospectData);
        XLSX.utils.book_append_sheet(workbook, prospectWs, 'Prospects');
      }

      // Tickets sheet
      if (data.tickets.length > 0) {
        const ticketData = data.tickets.map(t => ({
          'Ticket ID': t.id,
          'Customer': t.customer,
          'Subject': t.subject,
          'Priority': t.priority,
          'Category': t.category,
          'Status': t.status,
          'Assigned To': t.assignedTo,
          'Created Date': t.createdDate,
          'Last Update': t.lastUpdate
        }));
        const ticketWs = XLSX.utils.json_to_sheet(ticketData);
        XLSX.utils.book_append_sheet(workbook, ticketWs, 'Tickets');
      }

      // Invoices sheet
      if (data.invoices.length > 0) {
        const invoiceData = data.invoices.map(i => ({
          'Invoice ID': i.id,
          'Customer Name': i.customerName,
          'Issue Date': i.issueDate,
          'Due Date': i.dueDate,
          'Amount': i.amount,
          'Status': i.status,
          'Description': i.description
        }));
        const invoiceWs = XLSX.utils.json_to_sheet(invoiceData);
        XLSX.utils.book_append_sheet(workbook, invoiceWs, 'Invoices');
      }

      // Write the file
      XLSX.writeFile(workbook, `${filename}.xlsx`);
      console.log(`Successfully generated comprehensive report: ${filename}.xlsx`);
    } catch (error) {
      console.error('Error generating Excel report:', error);
      alert('Error generating Excel report. Please try again.');
    }
  } else {
    // PDF format - Single page layout
    try {
      const doc = new jsPDF('landscape', 'mm', 'a4'); // Landscape untuk lebih banyak ruang
      let yPosition = 15;

      // Title - lebih kecil
      doc.setFontSize(16);
      doc.setTextColor(124, 58, 237);
      doc.text('CRM Comprehensive Report', 15, yPosition);
      
      // Date - di sebelah kanan title
      doc.setFontSize(8);
      doc.setTextColor(100, 100, 100);
      doc.text(`Generated: ${new Date().toLocaleDateString()}`, 220, yPosition);
      yPosition += 12;

      // Summary statistics dalam format horizontal yang compact
      const summaryText = `Total: ${data.customers.length} Customers | ${data.prospects.length} Prospects | ${data.tickets.length} Tickets | ${data.invoices.length} Invoices | Active: ${data.customers.filter(c => c.status === 'Active').length} | Open Tickets: ${data.tickets.filter(t => t.status === 'Open').length}`;
      doc.setFontSize(7);
      doc.setTextColor(0, 0, 0);
      doc.text(summaryText, 15, yPosition);
      yPosition += 8;

      // Bagi halaman menjadi 4 kolom untuk setiap kategori data
      const pageWidth = 297; // A4 landscape width
      const columnWidth = (pageWidth - 40) / 4; // 4 kolom dengan margin
      const startX = 15;
      
      // Customers column
      if (data.customers.length > 0) {
        doc.setFontSize(10);
        doc.setTextColor(124, 58, 237);
        doc.text('CUSTOMERS', startX, yPosition);
        
        const customerData = data.customers.slice(0, 8).map(c => [
          (c.companyName || '').substring(0, 15),
          c.status || ''
        ]);
        
        autoTable(doc, {
          head: [['Company', 'Status']],
          body: customerData,
          startY: yPosition + 3,
          margin: { left: startX, right: pageWidth - startX - columnWidth },
          tableWidth: columnWidth - 5,
          theme: 'grid',
          headStyles: {
            fillColor: [124, 58, 237],
            textColor: [255, 255, 255],
            fontSize: 6,
            cellPadding: 1
          },
          bodyStyles: {
            fontSize: 5,
            cellPadding: 1
          },
          columnStyles: {
            0: { cellWidth: (columnWidth - 5) * 0.7 },
            1: { cellWidth: (columnWidth - 5) * 0.3 }
          }
        });
      }

      // Prospects column
      if (data.prospects.length > 0) {
        const prospectX = startX + columnWidth;
        doc.setFontSize(10);
        doc.setTextColor(124, 58, 237);
        doc.text('PROSPECTS', prospectX, yPosition);
        
        const prospectData = data.prospects.slice(0, 8).map(p => [
          (p.companyName || '').substring(0, 15),
          p.status || ''
        ]);
        
        autoTable(doc, {
          head: [['Company', 'Status']],
          body: prospectData,
          startY: yPosition + 3,
          margin: { left: prospectX, right: pageWidth - prospectX - columnWidth },
          tableWidth: columnWidth - 5,
          theme: 'grid',
          headStyles: {
            fillColor: [34, 197, 94],
            textColor: [255, 255, 255],
            fontSize: 6,
            cellPadding: 1
          },
          bodyStyles: {
            fontSize: 5,
            cellPadding: 1
          },
          columnStyles: {
            0: { cellWidth: (columnWidth - 5) * 0.7 },
            1: { cellWidth: (columnWidth - 5) * 0.3 }
          }
        });
      }

      // Tickets column
      if (data.tickets.length > 0) {
        const ticketX = startX + (columnWidth * 2);
        doc.setFontSize(10);
        doc.setTextColor(124, 58, 237);
        doc.text('TICKETS', ticketX, yPosition);
        
        const ticketData = data.tickets.slice(0, 8).map(t => [
          (t.subject || '').substring(0, 15),
          t.status || ''
        ]);
        
        autoTable(doc, {
          head: [['Subject', 'Status']],
          body: ticketData,
          startY: yPosition + 3,
          margin: { left: ticketX, right: pageWidth - ticketX - columnWidth },
          tableWidth: columnWidth - 5,
          theme: 'grid',
          headStyles: {
            fillColor: [239, 68, 68],
            textColor: [255, 255, 255],
            fontSize: 6,
            cellPadding: 1
          },
          bodyStyles: {
            fontSize: 5,
            cellPadding: 1
          },
          columnStyles: {
            0: { cellWidth: (columnWidth - 5) * 0.7 },
            1: { cellWidth: (columnWidth - 5) * 0.3 }
          }
        });
      }

      // Invoices column
      if (data.invoices.length > 0) {
        const invoiceX = startX + (columnWidth * 3);
        doc.setFontSize(10);
        doc.setTextColor(124, 58, 237);
        doc.text('INVOICES', invoiceX, yPosition);
        
        const invoiceData = data.invoices.slice(0, 8).map(i => [
          (i.customerName || '').substring(0, 15),
          i.status || ''
        ]);
        
        autoTable(doc, {
          head: [['Customer', 'Status']],
          body: invoiceData,
          startY: yPosition + 3,
          margin: { left: invoiceX, right: pageWidth - invoiceX - columnWidth },
          tableWidth: columnWidth - 5,
          theme: 'grid',
          headStyles: {
            fillColor: [251, 146, 60],
            textColor: [255, 255, 255],
            fontSize: 6,
            cellPadding: 1
          },
          bodyStyles: {
            fontSize: 5,
            cellPadding: 1
          },
          columnStyles: {
            0: { cellWidth: (columnWidth - 5) * 0.7 },
            1: { cellWidth: (columnWidth - 5) * 0.3 }
          }
        });
      }

      // Footer dengan informasi tambahan
      doc.setFontSize(6);
      doc.setTextColor(100, 100, 100);
      doc.text('CRM Wiznet - Comprehensive Business Report', 15, 200);
      doc.text(`Page 1 of 1 | Generated at ${new Date().toLocaleTimeString()}`, 220, 200);

      // Save PDF
      doc.save(`${filename}.pdf`);
      console.log(`Successfully generated single-page comprehensive report: ${filename}.pdf`);
    } catch (error) {
      console.error('Error generating PDF report:', error);
      alert('Error generating PDF report. Please try again.');
    }
  }
};
