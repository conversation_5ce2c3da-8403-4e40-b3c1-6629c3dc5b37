import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Download, Upload, FileSpreadsheet, FileText } from 'lucide-react';

interface ExportImportButtonsProps {
  onImport: () => void;
  onExportExcel: () => void;
  onExportPDF: () => void;
}

const ExportImportButtons: React.FC<ExportImportButtonsProps> = ({
  onImport,
  onExportExcel,
  onExportPDF,
}) => {
  const { isDark } = useTheme();

  return (
    <div className="flex space-x-2">
      <button
        onClick={onImport}
        className={`inline-flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
          isDark 
            ? 'bg-gray-700 text-white hover:bg-gray-600' 
            : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
        } focus:outline-none focus:ring-2 focus:ring-purple-500`}
      >
        <Upload className="mr-2 h-4 w-4" />
        Import
      </button>
      <button
        onClick={onExportExcel}
        className={`inline-flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
          isDark 
            ? 'bg-gray-700 text-white hover:bg-gray-600' 
            : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
        } focus:outline-none focus:ring-2 focus:ring-purple-500`}
      >
        <FileSpreadsheet className="mr-2 h-4 w-4" />
        Excel
      </button>
      <button
        onClick={onExportPDF}
        className={`inline-flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
          isDark 
            ? 'bg-gray-700 text-white hover:bg-gray-600' 
            : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
        } focus:outline-none focus:ring-2 focus:ring-purple-500`}
      >
        <FileText className="mr-2 h-4 w-4" />
        PDF
      </button>
    </div>
  );
};

export default ExportImportButtons;