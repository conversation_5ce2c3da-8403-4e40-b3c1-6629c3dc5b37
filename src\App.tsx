import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { useTheme } from './contexts/ThemeContext';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Customers from './pages/Customers';
import Prospects from './pages/Prospects';
import CustomerService from './pages/CustomerService';
import Assets from './pages/Assets';
import NetworkMonitoring from './pages/NetworkMonitoring';
import Invoices from './pages/Invoices';
import { initializeDatabase } from './hooks/useDatabase';

function App() {
  const { isDark } = useTheme();

  useEffect(() => {
    // Initialize database on app start
    initializeDatabase();
  }, []);

  return (
    <Router>
      <div className={`min-h-screen transition-colors ${
        isDark 
          ? 'bg-gray-950 text-white' 
          : 'bg-gray-50 text-gray-900'
      }`}>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/customers" element={<Customers />} />
            <Route path="/prospects" element={<Prospects />} />
            <Route path="/customer-service" element={<CustomerService />} />
            <Route path="/assets" element={<Assets />} />
            <Route path="/network-monitoring" element={<NetworkMonitoring />} />
            <Route path="/invoices" element={<Invoices />} />
          </Routes>
        </Layout>
      </div>
    </Router>
  );
}

export default App;