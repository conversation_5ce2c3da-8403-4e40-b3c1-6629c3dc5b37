import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface TicketFormProps {
  ticket?: any;
  onSave: (data: any) => void;
  onCancel: () => void;
}

const TicketForm: React.FC<TicketFormProps> = ({ ticket, onSave, onCancel }) => {
  const { isDark } = useTheme();
  const [formData, setFormData] = useState({
    customer: '',
    subject: '',
    description: '',
    priority: 'Medium',
    category: 'Technical',
    assignedTo: '',
    status: 'Open',
    startTrouble: '',
    finishedTrouble: '',
    rfo: '',
  });

  const priorities = ['Low', 'Medium', 'High', 'Critical'];
  const categories = [
    'Technical',
    'Billing',
    'General Inquiry',
    'Service Request',
    'Complaint',
    'Installation',
    'Maintenance',
  ];
  const statuses = ['Open', 'In Progress', 'Resolved', 'Closed'];
  const technicians = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];

  useEffect(() => {
    if (ticket) {
      setFormData({
        customer: ticket.customer || '',
        subject: ticket.subject || '',
        description: ticket.description || '',
        priority: ticket.priority || 'Medium',
        category: ticket.category || 'Technical',
        assignedTo: ticket.assignedTo || '',
        status: ticket.status || 'Open',
        startTrouble: ticket.startTrouble || '',
        finishedTrouble: ticket.finishedTrouble || '',
        rfo: ticket.rfo || '',
      });
    }
  }, [ticket]);

  const formatDate = (date: Date): string => {
    const day = date.getDate().toString().padStart(2, '0');
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const now = formatDate(new Date());
    const ticketData = {
      ...formData,
      id: ticket?.id || `TKT-${Date.now()}`,
      createdDate: ticket?.createdDate || now,
      lastUpdate: now,
    };
    onSave(ticketData);
  };

  const calculateDuration = (start: string, end: string): string => {
    if (!start || !end) return '';

    const startDate = new Date(start);
    const endDate = new Date(end);
    const diffMs = endDate.getTime() - startDate.getTime();

    if (diffMs < 0) return 'Invalid duration';

    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    return `${diffHours}h ${diffMinutes}m`;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label htmlFor="customer" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Customer *
          </label>
          <input
            type="text"
            id="customer"
            name="customer"
            required
            value={formData.customer}
            onChange={handleChange}
            placeholder="Enter customer name or company"
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>

        <div>
          <label htmlFor="priority" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Priority *
          </label>
          <select
            id="priority"
            name="priority"
            required
            value={formData.priority}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
          >
            {priorities.map((priority) => (
              <option key={priority} value={priority}>
                {priority}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div>
        <label htmlFor="subject" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
          Subject *
        </label>
        <input
          type="text"
          id="subject"
          name="subject"
          required
          value={formData.subject}
          onChange={handleChange}
          placeholder="Brief description of the issue"
          className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
        />
      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label htmlFor="category" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Category
          </label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
          >
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="assignedTo" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Assign To
          </label>
          <select
            id="assignedTo"
            name="assignedTo"
            value={formData.assignedTo}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
          >
            <option value="">Select technician</option>
            {technicians.map((tech) => (
              <option key={tech} value={tech}>
                {tech}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div>
        <label htmlFor="description" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
          Description *
        </label>
        <textarea
          id="description"
          name="description"
          rows={4}
          required
          value={formData.description}
          onChange={handleChange}
          placeholder="Detailed description of the issue or request"
          className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
        />
      </div>

      {ticket && (
        <div>
          <label htmlFor="status" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Status
          </label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
          >
            {statuses.map((status) => (
              <option key={status} value={status}>
                {status}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Trouble Timeline */}
      <div className="space-y-4">
        <h4 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'} border-b ${isDark ? 'border-gray-600' : 'border-gray-200'} pb-2`}>
          Trouble Timeline
        </h4>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="startTrouble" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Start Trouble
            </label>
            <input
              type="datetime-local"
              id="startTrouble"
              name="startTrouble"
              value={formData.startTrouble}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
            />
          </div>

          <div>
            <label htmlFor="finishedTrouble" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Finished Trouble
            </label>
            <input
              type="datetime-local"
              id="finishedTrouble"
              name="finishedTrouble"
              value={formData.finishedTrouble}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
            />
          </div>
        </div>

        {formData.startTrouble && formData.finishedTrouble && (
          <div className={`p-3 rounded-md ${isDark ? 'bg-gray-700' : 'bg-gray-50'}`}>
            <span className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Duration: {calculateDuration(formData.startTrouble, formData.finishedTrouble)}
            </span>
          </div>
        )}
      </div>

      {/* RFO Section */}
      <div>
        <label htmlFor="rfo" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
          RFO (Reason for Outage) - Ready for Broadcast
        </label>
        <textarea
          id="rfo"
          name="rfo"
          rows={4}
          value={formData.rfo}
          onChange={handleChange}
          placeholder="Dear Valued Customer,

We would like to inform you that there was a service disruption on [DATE] from [START TIME] to [END TIME] affecting [AFFECTED SERVICES].

Root Cause: [DESCRIBE THE ISSUE]
Resolution: [DESCRIBE THE SOLUTION]

We sincerely apologize for any inconvenience caused. Our team has implemented measures to prevent similar issues in the future.

Thank you for your understanding.

Best regards,
Technical Support Team"
          className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
        />
        <p className={`mt-1 text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
          This RFO template is ready to be broadcast to affected customers via email or other communication channels.
        </p>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            isDark 
              ? 'text-gray-300 bg-gray-700 border-gray-600 hover:bg-gray-600' 
              : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
          } border focus:outline-none focus:ring-2 focus:ring-purple-500`}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          {ticket ? 'Update Ticket' : 'Create Ticket'}
        </button>
      </div>
    </form>
  );
};

export default TicketForm;
