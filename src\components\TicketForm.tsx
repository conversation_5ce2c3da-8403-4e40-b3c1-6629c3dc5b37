import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface TicketFormProps {
  ticket?: any;
  onSave: (data: any) => void;
  onCancel: () => void;
}

const TicketForm: React.FC<TicketFormProps> = ({ ticket, onSave, onCancel }) => {
  const { isDark } = useTheme();
  const [formData, setFormData] = useState({
    customer: '',
    subject: '',
    description: '',
    priority: 'Medium',
    category: 'Technical',
    assignedTo: '',
    status: 'Open',
  });

  const priorities = ['Low', 'Medium', 'High', 'Critical'];
  const categories = [
    'Technical',
    'Billing',
    'General Inquiry',
    'Service Request',
    'Complaint',
    'Installation',
    'Maintenance',
  ];
  const statuses = ['Open', 'In Progress', 'Resolved', 'Closed'];
  const technicians = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];

  useEffect(() => {
    if (ticket) {
      setFormData({
        customer: ticket.customer || '',
        subject: ticket.subject || '',
        description: ticket.description || '',
        priority: ticket.priority || 'Medium',
        category: ticket.category || 'Technical',
        assignedTo: ticket.assignedTo || '',
        status: ticket.status || 'Open',
      });
    }
  }, [ticket]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const ticketData = {
      ...formData,
      id: ticket?.id || `TKT-${Date.now()}`,
      createdDate: ticket?.createdDate || new Date().toISOString().split('T')[0],
      lastUpdate: new Date().toISOString().split('T')[0],
    };
    onSave(ticketData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label htmlFor="customer" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Customer *
          </label>
          <input
            type="text"
            id="customer"
            name="customer"
            required
            value={formData.customer}
            onChange={handleChange}
            placeholder="Enter customer name or company"
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>

        <div>
          <label htmlFor="priority" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Priority *
          </label>
          <select
            id="priority"
            name="priority"
            required
            value={formData.priority}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
          >
            {priorities.map((priority) => (
              <option key={priority} value={priority}>
                {priority}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div>
        <label htmlFor="subject" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
          Subject *
        </label>
        <input
          type="text"
          id="subject"
          name="subject"
          required
          value={formData.subject}
          onChange={handleChange}
          placeholder="Brief description of the issue"
          className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
        />
      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label htmlFor="category" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Category
          </label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
          >
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="assignedTo" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Assign To
          </label>
          <select
            id="assignedTo"
            name="assignedTo"
            value={formData.assignedTo}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
          >
            <option value="">Select technician</option>
            {technicians.map((tech) => (
              <option key={tech} value={tech}>
                {tech}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div>
        <label htmlFor="description" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
          Description *
        </label>
        <textarea
          id="description"
          name="description"
          rows={4}
          required
          value={formData.description}
          onChange={handleChange}
          placeholder="Detailed description of the issue or request"
          className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
        />
      </div>

      {ticket && (
        <div>
          <label htmlFor="status" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Status
          </label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
          >
            {statuses.map((status) => (
              <option key={status} value={status}>
                {status}
              </option>
            ))}
          </select>
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            isDark 
              ? 'text-gray-300 bg-gray-700 border-gray-600 hover:bg-gray-600' 
              : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
          } border focus:outline-none focus:ring-2 focus:ring-purple-500`}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          {ticket ? 'Update Ticket' : 'Create Ticket'}
        </button>
      </div>
    </form>
  );
};

export default TicketForm;
