<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 140 35" xmlns="http://www.w3.org/2000/svg">
  <!-- Orange gradient matching the original -->
  <defs>
    <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF7A00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF6B35;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Wiznet logo icon - stylized W with curved elements -->
  <g transform="translate(3, 3)">
    <!-- Main curved W shape similar to original -->
    <path d="M2 6 Q4 8 6 12 Q8 16 10 20 Q12 16 14 12 Q16 8 18 6 Q20 8 22 12 Q24 16 26 20"
          stroke="url(#orangeGradient)"
          stroke-width="2.5"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"/>

    <!-- Additional curved elements for network/connectivity theme -->
    <path d="M4 10 Q8 8 12 10 Q16 12 20 10 Q24 8 28 10"
          stroke="url(#orangeGradient)"
          stroke-width="1.5"
          fill="none"
          opacity="0.6"
          stroke-linecap="round"/>
  </g>

  <!-- Wiznet text with proper styling -->
  <text x="38" y="20" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="url(#orangeGradient)">wiznet</text>
  <text x="38" y="28" font-family="Arial, sans-serif" font-size="7" fill="#666">Network Solutions</text>
</svg>
