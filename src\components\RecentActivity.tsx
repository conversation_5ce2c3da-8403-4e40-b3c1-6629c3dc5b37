import React from 'react';
import { Clock } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useTheme } from '../contexts/ThemeContext';

const RecentActivity: React.FC = () => {
  const { isDark } = useTheme();
  const activities = [
    {
      id: 1,
      type: 'customer',
      message: 'New customer PT Maju Jaya registered',
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      user: 'System',
    },
    {
      id: 2,
      type: 'ticket',
      message: 'Ticket #1234 resolved by Tech Support',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      user: '<PERSON>',
    },
    {
      id: 3,
      type: 'prospect',
      message: 'Prospect PT Digital Corp marked as Won',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      user: '<PERSON>',
    },
    {
      id: 4,
      type: 'invoice',
      message: 'Invoice #INV-2024-001 generated',
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
      user: 'System',
    },
  ];

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'customer':
        return 'bg-blue-500';
      case 'ticket':
        return 'bg-yellow-500';
      case 'prospect':
        return 'bg-green-500';
      case 'invoice':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border`}>
      <div className="p-6">
        <h3 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'} mb-4`}>Recent Activity</h3>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3">
              <div className={`h-2 w-2 rounded-full mt-2 ${getActivityColor(activity.type)}`}></div>
              <div className="flex-1 min-w-0">
                <p className={`text-sm ${isDark ? 'text-white' : 'text-gray-900'}`}>{activity.message}</p>
                <div className={`flex items-center mt-1 text-xs ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{formatDistanceToNow(activity.timestamp)} ago</span>
                  <span className="mx-2">•</span>
                  <span>{activity.user}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RecentActivity;