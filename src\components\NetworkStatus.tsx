import React from 'react';
import { Server, Wifi, Router, Shield } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

const NetworkStatus: React.FC = () => {
  const { isDark } = useTheme();
  const networkNodes = [
    { name: 'Core Router Jakarta', status: 'online', uptime: '99.9%', icon: Router },
    { name: 'Main Server', status: 'online', uptime: '99.8%', icon: Server },
    { name: 'WiFi Controller', status: 'warning', uptime: '98.2%', icon: Wifi },
    { name: 'Security Gateway', status: 'online', uptime: '99.9%', icon: Shield },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-400';
      case 'warning':
        return 'bg-yellow-400';
      case 'offline':
        return 'bg-red-400';
      default:
        return 'bg-gray-400';
    }
  };

  return (
    <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border`}>
      <div className="p-6">
        <h3 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'} mb-4`}>Network Status</h3>
        <div className="space-y-4">
          {networkNodes.map((node, index) => (
            <div key={index} className={`flex items-center justify-between p-4 ${isDark ? 'bg-gray-700' : 'bg-gray-50'} rounded-lg`}>
              <div className="flex items-center">
                <node.icon className={`h-5 w-5 ${isDark ? 'text-gray-400' : 'text-gray-600'} mr-3`} />
                <div>
                  <div className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>{node.name}</div>
                  <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Uptime: {node.uptime}</div>
                </div>
              </div>
              <div className="flex items-center">
                <div className={`h-2 w-2 rounded-full mr-2 ${getStatusColor(node.status)}`}></div>
                <span className={`text-sm capitalize ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>{node.status}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NetworkStatus;