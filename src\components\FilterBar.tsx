import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Filter, X } from 'lucide-react';

interface FilterOption {
  key: string;
  label: string;
  options: string[];
}

interface FilterBarProps {
  filters: FilterOption[];
  activeFilters: Record<string, string>;
  onFilterChange: (key: string, value: string) => void;
  onClearFilters: () => void;
}

const FilterBar: React.FC<FilterBarProps> = ({
  filters,
  activeFilters,
  onFilterChange,
  onClearFilters,
}) => {
  const { isDark } = useTheme();
  const hasActiveFilters = Object.values(activeFilters).some(value => value !== '');

  return (
    <div className={`p-4 border-b ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center">
          <Filter className={`h-4 w-4 mr-2 ${isDark ? 'text-gray-400' : 'text-gray-600'}`} />
          <span className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Filters:
          </span>
        </div>
        
        {filters.map((filter) => (
          <div key={filter.key} className="flex items-center">
            <select
              value={activeFilters[filter.key] || ''}
              onChange={(e) => onFilterChange(filter.key, e.target.value)}
              className={`text-sm rounded-md border ${
                isDark 
                  ? 'bg-gray-700 border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:border-purple-500 focus:ring-purple-500`}
            >
              <option value="">{filter.label}</option>
              {filter.options.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        ))}
        
        {hasActiveFilters && (
          <button
            onClick={onClearFilters}
            className={`flex items-center px-3 py-1 text-sm rounded-md transition-colors ${
              isDark 
                ? 'text-gray-400 hover:text-white hover:bg-gray-700' 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            <X className="h-3 w-3 mr-1" />
            Clear Filters
          </button>
        )}
      </div>
    </div>
  );
};

export default FilterBar;