import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { MapPin } from 'lucide-react';

interface CustomerFormProps {
  customer?: any;
  onSave: (data: any) => void;
  onCancel: () => void;
}

const CustomerForm: React.FC<CustomerFormProps> = ({ customer, onSave, onCancel }) => {
  const { isDark } = useTheme();
  const [formData, setFormData] = useState({
    companyName: '',
    picName: '',
    picTechnical: '',
    picTechnicalPhone: '',
    picTechnicalEmail: '',
    email: '',
    phone: '',
    customPackage: '',
    status: 'Active',
    monthlyFee: '',
    address: '',
    latitude: '',
    longitude: '',
    notes: '',
  });

  useEffect(() => {
    if (customer) {
      setFormData({
        companyName: customer.companyName || '',
        picName: customer.picName || '',
        picTechnical: customer.picTechnical || '',
        picTechnicalPhone: customer.picTechnicalPhone || '',
        picTechnicalEmail: customer.picTechnicalEmail || '',
        email: customer.email || '',
        phone: customer.phone || '',
        customPackage: customer.customPackage || customer.package || '',
        status: customer.status || 'Active',
        monthlyFee: customer.monthlyFee || '',
        address: customer.address || '',
        latitude: customer.latitude || '',
        longitude: customer.longitude || '',
        notes: customer.notes || '',
      });
    }
  }, [customer]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const openGoogleMaps = () => {
    if (formData.latitude && formData.longitude) {
      const url = `https://www.google.com/maps?q=${formData.latitude},${formData.longitude}`;
      window.open(url, '_blank');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label htmlFor="companyName" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Company Name *
          </label>
          <input
            type="text"
            id="companyName"
            name="companyName"
            required
            value={formData.companyName}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="picName" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            PIC Name *
          </label>
          <input
            type="text"
            id="picName"
            name="picName"
            required
            value={formData.picName}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="picTechnical" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Technical PIC
          </label>
          <input
            type="text"
            id="picTechnical"
            name="picTechnical"
            value={formData.picTechnical}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="picTechnicalPhone" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Technical PIC Phone
          </label>
          <input
            type="tel"
            id="picTechnicalPhone"
            name="picTechnicalPhone"
            value={formData.picTechnicalPhone}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="picTechnicalEmail" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Technical PIC Email
          </label>
          <input
            type="email"
            id="picTechnicalEmail"
            name="picTechnicalEmail"
            value={formData.picTechnicalEmail}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="email" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Email *
          </label>
          <input
            type="email"
            id="email"
            name="email"
            required
            value={formData.email}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="phone" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Phone *
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            required
            value={formData.phone}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="customPackage" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Custom Package *
          </label>
          <input
            type="text"
            id="customPackage"
            name="customPackage"
            required
            placeholder="e.g., Business Pro 100 Mbps"
            value={formData.customPackage}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="status" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Status
          </label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
          >
            <option value="Active">Active</option>
            <option value="Suspended">Suspended</option>
            <option value="Terminated">Terminated</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="monthlyFee" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Monthly Fee
          </label>
          <input
            type="text"
            id="monthlyFee"
            name="monthlyFee"
            placeholder="Rp 2,500,000"
            value={formData.monthlyFee}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="latitude" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Latitude
          </label>
          <input
            type="text"
            id="latitude"
            name="latitude"
            placeholder="-6.2088"
            value={formData.latitude}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
        
        <div>
          <label htmlFor="longitude" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Longitude
          </label>
          <div className="flex">
            <input
              type="text"
              id="longitude"
              name="longitude"
              placeholder="106.8456"
              value={formData.longitude}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-l-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
            />
            {formData.latitude && formData.longitude && (
              <button
                type="button"
                onClick={openGoogleMaps}
                className="mt-1 px-3 py-2 bg-purple-600 text-white rounded-r-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <MapPin className="h-4 w-4" />
              </button>
            )}
          </div>
          />
        </div>
      </div>
      
      <div>
        <label htmlFor="address" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
          Address
        </label>
        <textarea
          id="address"
          name="address"
          rows={3}
          value={formData.address}
          onChange={handleChange}
          className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
        />
      </div>
      
      <div>
        <label htmlFor="notes" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
          Notes
        </label>
        <textarea
          id="notes"
          name="notes"
          rows={2}
          value={formData.notes}
          onChange={handleChange}
          className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
        />
      </div>
      
      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className={`px-4 py-2 text-sm font-medium ${isDark ? 'text-gray-300 bg-gray-700 border-gray-600 hover:bg-gray-600' : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'} border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500`}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          {customer ? 'Update' : 'Create'} Customer
        </button>
      </div>
    </form>
  );
};

export default CustomerForm;