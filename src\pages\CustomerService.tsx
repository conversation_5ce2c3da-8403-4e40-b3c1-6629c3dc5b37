import React, { useState } from 'react';
import { Plus, Search, MessageSquare, Megaphone, Ban, Clock } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import DataTable from '../components/DataTable';
import Modal from '../components/Modal';
import TicketForm from '../components/TicketForm';

const CustomerService: React.FC = () => {
  const { isDark } = useTheme();
  const [activeTab, setActiveTab] = useState('tickets');
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTicket, setEditingTicket] = useState<any>(null);

  const tickets = [
    {
      id: 'TKT-001',
      customer: 'PT Teknologi Maju',
      subject: 'Internet connection slow',
      priority: 'High',
      status: 'Open',
      assignedTo: '<PERSON>',
      createdDate: '2024-01-20',
      lastUpdate: '2024-01-21',
    },
    {
      id: 'TKT-002',
      customer: 'CV Digital Creative',
      subject: 'Email server not accessible',
      priority: 'Medium',
      status: 'In Progress',
      assignedTo: '<PERSON>',
      createdDate: '2024-01-19',
      lastUpdate: '2024-01-21',
    },
  ];

  const announcements = [
    {
      id: 1,
      title: 'Maintenance Schedule - Jakarta Node',
      content: 'Network maintenance on January 25th from 2-4 AM',
      targetCustomers: 'All Jakarta customers',
      status: 'Published',
      createdDate: '2024-01-18',
    },
    {
      id: 2,
      title: 'Service Upgrade Notification',
      content: 'Speed upgrade for Business Pro packages',
      targetCustomers: 'Business Pro customers',
      status: 'Draft',
      createdDate: '2024-01-20',
    },
  ];

  const tabs = [
    { id: 'tickets', name: 'Trouble Tickets', icon: MessageSquare },
    { id: 'announcements', name: 'Announcements', icon: Megaphone },
    { id: 'suspensions', name: 'Account Management', icon: Ban },
  ];

  const ticketColumns = [
    { key: 'id', label: 'Ticket ID' },
    { key: 'customer', label: 'Customer' },
    { key: 'subject', label: 'Subject' },
    { key: 'priority', label: 'Priority' },
    { key: 'status', label: 'Status' },
    { key: 'assignedTo', label: 'Assigned To' },
    { key: 'createdDate', label: 'Created Date' },
  ];

  const announcementColumns = [
    { key: 'title', label: 'Title' },
    { key: 'targetCustomers', label: 'Target' },
    { key: 'status', label: 'Status' },
    { key: 'createdDate', label: 'Created Date' },
  ];

  const handleSaveTicket = (data: any) => {
    console.log('Save ticket:', data);
    // Here you would typically save the ticket to your backend
    alert('Ticket created successfully!');
    setIsModalOpen(false);
    setEditingTicket(null);
  };

  const handleCancelTicket = () => {
    setIsModalOpen(false);
    setEditingTicket(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className={`text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>Customer Service</h1>
          <p className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            Manage customer support tickets and communications
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => setIsModalOpen(true)}
            className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 transition-colors"
          >
            <Plus className="mr-2 h-4 w-4" />
            {activeTab === 'tickets' ? 'New Ticket' : activeTab === 'announcements' ? 'New Announcement' : 'Suspend Account'}
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-4">
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <MessageSquare className="h-6 w-6 text-blue-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>23</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Open Tickets</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Clock className="h-6 w-6 text-yellow-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>2.5h</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Avg Response Time</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Megaphone className="h-6 w-6 text-green-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>3</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Active Announcements</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Ban className="h-6 w-6 text-red-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>5</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Suspended Accounts</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border`}>
        <div className={`${isDark ? 'border-b border-gray-700' : 'border-b border-gray-200'}`}>
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-purple-500 text-purple-400'
                    : isDark
                      ? 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                      : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300'
                } whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium flex items-center transition-colors`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <div className="relative">
              <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${isDark ? 'text-gray-400' : 'text-gray-500'} h-4 w-4`} />
              <input
                type="text"
                placeholder={`Search ${activeTab}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                  isDark
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
                } border`}
              />
            </div>
          </div>

          {activeTab === 'tickets' && (
            <DataTable
              columns={ticketColumns}
              data={tickets}
              emptyMessage="No tickets found"
            />
          )}

          {activeTab === 'announcements' && (
            <DataTable
              columns={announcementColumns}
              data={announcements}
              emptyMessage="No announcements found"
            />
          )}

          {activeTab === 'suspensions' && (
            <div className="text-center py-12">
              <Ban className={`h-12 w-12 ${isDark ? 'text-gray-400' : 'text-gray-500'} mx-auto mb-4`} />
              <h3 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'} mb-2`}>Account Management</h3>
              <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'} mb-6`}>Manage customer account suspensions and blocks</p>
              <button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                Suspend Account
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Modal for New Ticket */}
      {activeTab === 'tickets' && (
        <Modal
          isOpen={isModalOpen}
          onClose={handleCancelTicket}
          title={editingTicket ? 'Edit Ticket' : 'Create New Ticket'}
        >
          <TicketForm
            ticket={editingTicket}
            onSave={handleSaveTicket}
            onCancel={handleCancelTicket}
          />
        </Modal>
      )}
    </div>
  );
};

export default CustomerService;