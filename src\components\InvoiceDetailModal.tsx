import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { X, Send, Download } from 'lucide-react';

interface InvoiceDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: any;
  onSendEmail: (invoice: any) => void;
}

const InvoiceDetailModal: React.FC<InvoiceDetailModalProps> = ({
  isOpen,
  onClose,
  invoice,
  onSendEmail,
}) => {
  const { isDark } = useTheme();

  if (!isOpen || !invoice) return null;

  const handleSendEmail = () => {
    onSendEmail(invoice);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
        
        <div className={`relative inline-block align-bottom ${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6 border`}>
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className={`${isDark ? 'bg-gray-800 text-gray-400 hover:text-gray-300' : 'bg-white text-gray-400 hover:text-gray-600'} rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500`}
              onClick={onClose}
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          
          <div className="sm:flex sm:items-start">
            <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <h3 className={`text-lg leading-6 font-medium ${isDark ? 'text-white' : 'text-gray-900'} mb-6`}>
                Invoice Details - {invoice.id}
              </h3>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'} mb-2`}>Customer Information</h4>
                    <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-lg`}>
                      <p className={`font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>{invoice.customerName}</p>
                      <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'} mt-1`}>{invoice.description}</p>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'} mb-2`}>Invoice Information</h4>
                    <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-lg space-y-2`}>
                      <div className="flex justify-between">
                        <span className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Amount:</span>
                        <span className={`font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>{invoice.amount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Issue Date:</span>
                        <span className={`${isDark ? 'text-gray-300' : 'text-gray-700'}`}>{invoice.issueDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Due Date:</span>
                        <span className={`${isDark ? 'text-gray-300' : 'text-gray-700'}`}>{invoice.dueDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Status:</span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          invoice.status === 'Paid' 
                            ? (isDark ? 'bg-green-900 text-green-300' : 'bg-green-100 text-green-800')
                            : invoice.status === 'Pending'
                            ? (isDark ? 'bg-yellow-900 text-yellow-300' : 'bg-yellow-100 text-yellow-800')
                            : (isDark ? 'bg-red-900 text-red-300' : 'bg-red-100 text-red-800')
                        }`}>
                          {invoice.status}
                        </span>
                      </div>
                      {invoice.paymentDate && (
                        <div className="flex justify-between">
                          <span className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Payment Date:</span>
                          <span className={`${isDark ? 'text-gray-300' : 'text-gray-700'}`}>{invoice.paymentDate}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className={`flex justify-end space-x-3 pt-4 border-t ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
                  <button
                    onClick={() => console.log('Download invoice:', invoice.id)}
                    className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                      isDark 
                        ? 'text-gray-300 bg-gray-700 border-gray-600 hover:bg-gray-600' 
                        : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
                    } border focus:outline-none focus:ring-2 focus:ring-purple-500`}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download PDF
                  </button>
                  <button
                    onClick={handleSendEmail}
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <Send className="mr-2 h-4 w-4" />
                    Send Email
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceDetailModal;