import React, { useState } from 'react';
import { Plus, Search, Download, Upload, Package, Router, Server, Wifi } from 'lucide-react';
import DataTable from '../components/DataTable';

const Assets: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const assets = [
    {
      id: 'AST-001',
      name: 'Core Router Jakarta-01',
      type: 'Router',
      model: 'Cisco ASR-9000',
      serialNumber: 'CSC123456789',
      location: 'Jakarta Data Center',
      status: 'Active',
      installDate: '2023-06-15',
      warrantyExpiry: '2026-06-15',
      assignedTo: 'PT Teknologi Maju',
    },
    {
      id: 'AST-002',
      name: 'Access Point Building-A',
      type: 'Access Point',
      model: 'Ubiquiti UniFi AP',
      serialNumber: 'UBI987654321',
      location: 'Building A Floor 3',
      status: 'Active',
      installDate: '2023-08-20',
      warrantyExpiry: '2025-08-20',
      assignedTo: 'CV Digital Creative',
    },
    {
      id: 'AST-003',
      name: 'Backup Server',
      type: 'Server',
      model: 'Dell PowerEdge R730',
      serialNumber: 'DEL456789123',
      location: 'Surabaya Data Center',
      status: 'Maintenance',
      installDate: '2023-03-10',
      warrantyExpiry: '2026-03-10',
      assignedTo: 'Internal Use',
    },
  ];

  const columns = [
    { key: 'id', label: 'Asset ID' },
    { key: 'name', label: 'Asset Name' },
    { key: 'type', label: 'Type' },
    { key: 'model', label: 'Model' },
    { key: 'location', label: 'Location' },
    { key: 'status', label: 'Status' },
    { key: 'assignedTo', label: 'Assigned To' },
    { key: 'warrantyExpiry', label: 'Warranty Expiry' },
  ];

  const assetStats = {
    total: assets.length,
    active: assets.filter(a => a.status === 'Active').length,
    maintenance: assets.filter(a => a.status === 'Maintenance').length,
    routers: assets.filter(a => a.type === 'Router').length,
  };

  const filteredAssets = assets.filter(asset =>
    asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    asset.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    asset.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Asset Management</h1>
          <p className="mt-1 text-sm text-gray-400">
            Track and manage network equipment and infrastructure
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="inline-flex items-center rounded-lg bg-gray-700 px-4 py-2 text-sm font-medium text-white hover:bg-gray-600">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </button>
          <button className="inline-flex items-center rounded-lg bg-gray-700 px-4 py-2 text-sm font-medium text-white hover:bg-gray-600">
            <Download className="mr-2 h-4 w-4" />
            Export
          </button>
          <button className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700">
            <Plus className="mr-2 h-4 w-4" />
            Add Asset
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <Package className="h-6 w-6 text-blue-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">{assetStats.total}</div>
              <div className="text-sm text-gray-400">Total Assets</div>
            </div>
          </div>
        </div>
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <Router className="h-6 w-6 text-green-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">{assetStats.active}</div>
              <div className="text-sm text-gray-400">Active Assets</div>
            </div>
          </div>
        </div>
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <Server className="h-6 w-6 text-yellow-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">{assetStats.maintenance}</div>
              <div className="text-sm text-gray-400">Under Maintenance</div>
            </div>
          </div>
        </div>
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <Wifi className="h-6 w-6 text-purple-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">{assetStats.routers}</div>
              <div className="text-sm text-gray-400">Network Routers</div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg border border-gray-700">
        <div className="p-6 border-b border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search assets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        <DataTable
          columns={columns}
          data={filteredAssets}
          emptyMessage="No assets found"
        />
      </div>
    </div>
  );
};

export default Assets;