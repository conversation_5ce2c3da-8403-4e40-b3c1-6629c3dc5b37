import React, { useState } from 'react';
import { Plus, Search, Download, Upload, Package, Router, Server, Wifi } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import DataTable from '../components/DataTable';

const Assets: React.FC = () => {
  const { isDark } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');

  const assets = [
    {
      id: 'AST-001',
      name: 'Core Router Jakarta-01',
      type: 'Router',
      model: 'Cisco ASR-9000',
      serialNumber: 'CSC123456789',
      location: 'Jakarta Data Center',
      status: 'Active',
      installDate: '2023-06-15',
      warrantyExpiry: '2026-06-15',
      assignedTo: 'PT Teknologi Maju',
    },
    {
      id: 'AST-002',
      name: 'Access Point Building-A',
      type: 'Access Point',
      model: 'Ubiquiti UniFi AP',
      serialNumber: 'UBI987654321',
      location: 'Building A Floor 3',
      status: 'Active',
      installDate: '2023-08-20',
      warrantyExpiry: '2025-08-20',
      assignedTo: 'CV Digital Creative',
    },
    {
      id: 'AST-003',
      name: 'Backup Server',
      type: 'Server',
      model: 'Dell PowerEdge R730',
      serialNumber: 'DEL456789123',
      location: 'Surabaya Data Center',
      status: 'Maintenance',
      installDate: '2023-03-10',
      warrantyExpiry: '2026-03-10',
      assignedTo: 'Internal Use',
    },
  ];

  const columns = [
    { key: 'id', label: 'Asset ID' },
    { key: 'name', label: 'Asset Name' },
    { key: 'type', label: 'Type' },
    { key: 'model', label: 'Model' },
    { key: 'location', label: 'Location' },
    { key: 'status', label: 'Status' },
    { key: 'assignedTo', label: 'Assigned To' },
    { key: 'warrantyExpiry', label: 'Warranty Expiry' },
  ];

  const assetStats = {
    total: assets.length,
    active: assets.filter(a => a.status === 'Active').length,
    maintenance: assets.filter(a => a.status === 'Maintenance').length,
    routers: assets.filter(a => a.type === 'Router').length,
  };

  const filteredAssets = assets.filter(asset =>
    asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    asset.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    asset.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className={`text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>Asset Management</h1>
          <p className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            Track and manage network equipment and infrastructure
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className={`inline-flex items-center rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
            isDark
              ? 'bg-gray-700 text-white hover:bg-gray-600'
              : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
          }`}>
            <Upload className="mr-2 h-4 w-4" />
            Import
          </button>
          <button className={`inline-flex items-center rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
            isDark
              ? 'bg-gray-700 text-white hover:bg-gray-600'
              : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
          }`}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </button>
          <button className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 transition-colors">
            <Plus className="mr-2 h-4 w-4" />
            Add Asset
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-4">
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Package className="h-6 w-6 text-blue-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{assetStats.total}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Total Assets</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Router className="h-6 w-6 text-green-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{assetStats.active}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Active Assets</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Server className="h-6 w-6 text-yellow-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{assetStats.maintenance}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Under Maintenance</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Wifi className="h-6 w-6 text-purple-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{assetStats.routers}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Network Routers</div>
            </div>
          </div>
        </div>
      </div>

      <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border`}>
        <div className={`p-6 ${isDark ? 'border-b border-gray-700' : 'border-b border-gray-200'}`}>
          <div className="relative">
            <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${isDark ? 'text-gray-400' : 'text-gray-500'} h-4 w-4`} />
            <input
              type="text"
              placeholder="Search assets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                isDark
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
              } border`}
            />
          </div>
        </div>

        <DataTable
          columns={columns}
          data={filteredAssets}
          emptyMessage="No assets found"
        />
      </div>
    </div>
  );
};

export default Assets;