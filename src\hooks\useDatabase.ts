import { useState, useEffect } from 'react';
import { db, Customer, Prospect, Ticket, Invoice } from '../utils/database';

// Custom hook for customers
export const useCustomers = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = () => {
    setLoading(true);
    const data = db.getCustomers();
    setCustomers(data);
    setLoading(false);
  };

  const addCustomer = (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newCustomer = db.createCustomer(customer);
    setCustomers(prev => [...prev, newCustomer]);
    return newCustomer;
  };

  const updateCustomer = (id: string, updates: Partial<Omit<Customer, 'id' | 'createdAt'>>) => {
    const updatedCustomer = db.updateCustomer(id, updates);
    if (updatedCustomer) {
      setCustomers(prev => prev.map(c => c.id === id ? updatedCustomer : c));
    }
    return updatedCustomer;
  };

  const removeCustomer = (id: string) => {
    const success = db.deleteCustomer(id);
    if (success) {
      setCustomers(prev => prev.filter(c => c.id !== id));
    }
    return success;
  };

  return {
    customers,
    loading,
    addCustomer,
    updateCustomer,
    removeCustomer,
    refreshCustomers: loadCustomers,
  };
};

// Custom hook for prospects
export const useProspects = () => {
  const [prospects, setProspects] = useState<Prospect[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProspects();
  }, []);

  const loadProspects = () => {
    setLoading(true);
    const data = db.getProspects();
    setProspects(data);
    setLoading(false);
  };

  const addProspect = (prospect: Omit<Prospect, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProspect = db.createProspect(prospect);
    setProspects(prev => [...prev, newProspect]);
    return newProspect;
  };

  const updateProspect = (id: string, updates: Partial<Omit<Prospect, 'id' | 'createdAt'>>) => {
    const updatedProspect = db.updateProspect(id, updates);
    if (updatedProspect) {
      setProspects(prev => prev.map(p => p.id === id ? updatedProspect : p));
    }
    return updatedProspect;
  };

  const removeProspect = (id: string) => {
    const success = db.deleteProspect(id);
    if (success) {
      setProspects(prev => prev.filter(p => p.id !== id));
    }
    return success;
  };

  return {
    prospects,
    loading,
    addProspect,
    updateProspect,
    removeProspect,
    refreshProspects: loadProspects,
  };
};

// Custom hook for tickets
export const useTickets = () => {
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTickets();
  }, []);

  const loadTickets = () => {
    setLoading(true);
    const data = db.getTickets();
    setTickets(data);
    setLoading(false);
  };

  const addTicket = (ticket: Omit<Ticket, 'id' | 'createdDate' | 'lastUpdate'>) => {
    const newTicket = db.createTicket(ticket);
    setTickets(prev => [...prev, newTicket]);
    return newTicket;
  };

  const updateTicket = (id: string, updates: Partial<Omit<Ticket, 'id' | 'createdDate'>>) => {
    const updatedTicket = db.updateTicket(id, updates);
    if (updatedTicket) {
      setTickets(prev => prev.map(t => t.id === id ? updatedTicket : t));
    }
    return updatedTicket;
  };

  const removeTicket = (id: string) => {
    const success = db.deleteTicket(id);
    if (success) {
      setTickets(prev => prev.filter(t => t.id !== id));
    }
    return success;
  };

  return {
    tickets,
    loading,
    addTicket,
    updateTicket,
    removeTicket,
    refreshTickets: loadTickets,
  };
};

// Custom hook for invoices
export const useInvoices = () => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = () => {
    setLoading(true);
    const data = db.getInvoices();
    setInvoices(data);
    setLoading(false);
  };

  const addInvoice = (invoice: Omit<Invoice, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newInvoice = db.createInvoice(invoice);
    setInvoices(prev => [...prev, newInvoice]);
    return newInvoice;
  };

  const updateInvoice = (id: string, updates: Partial<Omit<Invoice, 'id' | 'createdAt'>>) => {
    const updatedInvoice = db.updateInvoice(id, updates);
    if (updatedInvoice) {
      setInvoices(prev => prev.map(i => i.id === id ? updatedInvoice : i));
    }
    return updatedInvoice;
  };

  const removeInvoice = (id: string) => {
    const success = db.deleteInvoice(id);
    if (success) {
      setInvoices(prev => prev.filter(i => i.id !== id));
    }
    return success;
  };

  return {
    invoices,
    loading,
    addInvoice,
    updateInvoice,
    removeInvoice,
    refreshInvoices: loadInvoices,
  };
};

// Initialize sample data on first load
export const initializeDatabase = () => {
  db.initializeSampleData();
};

export const clearDatabase = () => {
  db.clearAllData();
};

export const resetDatabase = () => {
  db.clearAllData();
  db.initializeSampleData();
};
