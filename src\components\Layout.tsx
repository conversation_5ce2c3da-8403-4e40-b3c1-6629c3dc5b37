import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { 
  LayoutDashboard, 
  Users, 
  Target, 
  Headphones, 
  Package, 
  Monitor, 
  FileText,
  Menu,
  X,
  Sun,
  Moon
} from 'lucide-react';

// Wiznet Logo Component
const WiznetLogo: React.FC<{ className?: string }> = ({ className = "h-8 w-8" }) => (
  <img
    src="/wiznet-logo.svg"
    alt="Wiznet"
    className={className}
    style={{ objectFit: 'contain' }}
  />
);

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isDark, toggleTheme } = useTheme();

  const navigation = [
    { name: 'Dashboard', href: '/', icon: LayoutDashboard },
    { name: 'Customers', href: '/customers', icon: Users },
    { name: 'Prospects', href: '/prospects', icon: Target },
    { name: 'Customer Service', href: '/customer-service', icon: Headphones },
    { name: 'Assets', href: '/assets', icon: Package },
    { name: 'Network Monitoring', href: '/network-monitoring', icon: Monitor },
    { name: 'Invoices', href: '/invoices', icon: FileText },
  ];

  return (
    <div className={`flex h-screen ${isDark ? 'bg-gray-950' : 'bg-gray-50'}`}>
      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className={`fixed inset-0 ${isDark ? 'bg-gray-900' : 'bg-gray-100'} bg-opacity-75`} />
          <div className={`fixed inset-y-0 left-0 flex w-full max-w-xs flex-col ${isDark ? 'bg-gray-900' : 'bg-white border-r border-gray-200'}`}>
            <div className="flex h-16 shrink-0 items-center justify-between px-4">
              <div className="flex items-center space-x-2">
                <WiznetLogo className="h-6 w-auto" />
              </div>
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className={`${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`}
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <nav className="flex flex-1 flex-col px-4 pb-4">
              <ul className="flex flex-1 flex-col gap-y-7">
                <li>
                  <ul className="-mx-2 space-y-1">
                    {navigation.map((item) => (
                      <li key={item.name}>
                        <NavLink
                          to={item.href}
                          onClick={() => setIsMobileMenuOpen(false)}
                          className={({ isActive }) =>
                            `group flex gap-x-3 rounded-lg p-3 text-sm font-medium leading-6 transition-colors ${
                              isActive
                                ? 'bg-purple-600 text-white'
                                : isDark 
                                  ? 'text-gray-300 hover:bg-gray-800 hover:text-white'
                                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                            }`
                          }
                        >
                          <item.icon className="h-5 w-5 shrink-0" />
                          {item.name}
                        </NavLink>
                      </li>
                    ))}
                  </ul>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      )}

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className={`flex grow flex-col gap-y-5 overflow-y-auto ${isDark ? 'bg-gray-900' : 'bg-white border-r border-gray-200'} px-6 pb-4`}>
          <div className="flex h-16 shrink-0 items-center justify-center">
            <WiznetLogo className="h-10 w-auto" />
          </div>
          <nav className="flex flex-1 flex-col">
            <ul className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <NavLink
                        to={item.href}
                        className={({ isActive }) =>
                          `group flex gap-x-3 rounded-lg p-3 text-sm font-medium leading-6 transition-colors ${
                            isActive
                              ? 'bg-purple-600 text-white'
                              : isDark 
                                ? 'text-gray-300 hover:bg-gray-800 hover:text-white'
                                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                          }`
                        }
                      >
                        <item.icon className="h-5 w-5 shrink-0" />
                        {item.name}
                      </NavLink>
                    </li>
                  ))}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-72 flex-1 flex flex-col">
        {/* Top bar */}
        <div className={`sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 ${
          isDark 
            ? 'border-b border-gray-800 bg-gray-900' 
            : 'border-b border-gray-200 bg-white'
        } px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8`}>
          <button
            type="button"
            className={`-m-2.5 p-2.5 ${isDark ? 'text-gray-400' : 'text-gray-600'} lg:hidden`}
            onClick={() => setIsMobileMenuOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1"></div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              <button
                onClick={toggleTheme}
                className={`p-2 rounded-lg transition-colors ${
                  isDark 
                    ? 'text-gray-400 hover:text-white hover:bg-gray-800' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                {isDark ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </button>
              <div className={`hidden lg:block lg:h-6 lg:w-px ${isDark ? 'lg:bg-gray-800' : 'lg:bg-gray-200'}`} />
              <div className="relative">
                <div className="flex items-center space-x-3">
                  <div className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                    <div className="font-medium">Admin User</div>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-purple-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">A</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 overflow-y-auto">
          <div className="py-8">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;