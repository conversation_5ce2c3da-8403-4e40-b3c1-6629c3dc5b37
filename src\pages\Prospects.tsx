import React, { useState } from 'react';
import { Plus, Search, Download, Upload, Edit, Trash2, Target } from 'lucide-react';
import DataTable from '../components/DataTable';
import Modal from '../components/Modal';
import ProspectForm from '../components/ProspectForm';

const Prospects: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingProspect, setEditingProspect] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const prospects = [
    {
      id: 1,
      companyName: 'PT Future Tech',
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '+62-21-5555555',
      source: 'Website',
      status: 'Qualified',
      estimatedValue: 'Rp 5,000,000',
      followUpDate: '2024-01-25',
      notes: 'Interested in Enterprise package',
    },
    {
      id: 2,
      companyName: 'CV Inovasi Digital',
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '+62-21-6666666',
      source: 'Referral',
      status: 'Won',
      estimatedValue: 'Rp 3,500,000',
      followUpDate: '2024-01-20',
      notes: 'Converted to customer',
    },
    {
      id: 3,
      companyName: 'PT Global Connect',
      contactPerson: 'Rudi Setiawan',
      email: '<EMAIL>',
      phone: '+62-21-7777777',
      source: 'Cold Call',
      status: 'Lost',
      estimatedValue: 'Rp 8,000,000',
      followUpDate: '2024-01-15',
      notes: 'Chose competitor due to pricing',
    },
  ];

  const columns = [
    { key: 'companyName', label: 'Company Name' },
    { key: 'contactPerson', label: 'Contact Person' },
    { key: 'email', label: 'Email' },
    { key: 'source', label: 'Source' },
    { key: 'status', label: 'Status' },
    { key: 'estimatedValue', label: 'Est. Value' },
    { key: 'followUpDate', label: 'Follow Up Date' },
  ];

  const handleEdit = (prospect: any) => {
    setEditingProspect(prospect);
    setIsModalOpen(true);
  };

  const handleDelete = (prospectId: number) => {
    if (confirm('Are you sure you want to delete this prospect?')) {
      console.log('Delete prospect:', prospectId);
    }
  };

  const actions = [
    {
      icon: Edit,
      label: 'Edit',
      onClick: handleEdit,
      className: 'text-blue-400 hover:text-blue-300',
    },
    {
      icon: Trash2,
      label: 'Delete',
      onClick: (item: any) => handleDelete(item.id),
      className: 'text-red-400 hover:text-red-300',
    },
  ];

  const filteredProspects = prospects.filter(prospect =>
    prospect.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    prospect.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
    prospect.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const statusStats = {
    qualified: prospects.filter(p => p.status === 'Qualified').length,
    won: prospects.filter(p => p.status === 'Won').length,
    lost: prospects.filter(p => p.status === 'Lost').length,
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Prospect Management</h1>
          <p className="mt-1 text-sm text-gray-400">
            Track and manage sales prospects and opportunities
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="inline-flex items-center rounded-lg bg-gray-700 px-4 py-2 text-sm font-medium text-white hover:bg-gray-600">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </button>
          <button className="inline-flex items-center rounded-lg bg-gray-700 px-4 py-2 text-sm font-medium text-white hover:bg-gray-600">
            <Download className="mr-2 h-4 w-4" />
            Export
          </button>
          <button
            onClick={() => setIsModalOpen(true)}
            className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Prospect
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <Target className="h-6 w-6 text-blue-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">{statusStats.qualified}</div>
              <div className="text-sm text-gray-400">Qualified Prospects</div>
            </div>
          </div>
        </div>
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <Target className="h-6 w-6 text-green-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">{statusStats.won}</div>
              <div className="text-sm text-gray-400">Won Deals</div>
            </div>
          </div>
        </div>
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center">
            <Target className="h-6 w-6 text-red-400" />
            <div className="ml-4">
              <div className="text-2xl font-semibold text-white">{statusStats.lost}</div>
              <div className="text-sm text-gray-400">Lost Deals</div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg border border-gray-700">
        <div className="p-6 border-b border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search prospects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        <DataTable
          columns={columns}
          data={filteredProspects}
          actions={actions}
          emptyMessage="No prospects found"
        />
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingProspect(null);
        }}
        title={editingProspect ? 'Edit Prospect' : 'Add New Prospect'}
      >
        <ProspectForm
          prospect={editingProspect}
          onSave={(data) => {
            console.log('Save prospect:', data);
            setIsModalOpen(false);
            setEditingProspect(null);
          }}
          onCancel={() => {
            setIsModalOpen(false);
            setEditingProspect(null);
          }}
        />
      </Modal>
    </div>
  );
};

export default Prospects;