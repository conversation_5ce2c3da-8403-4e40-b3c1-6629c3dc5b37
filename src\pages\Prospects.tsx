import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Plus, Search, Import, Download, FileText, Upload, Edit, Trash2, Target } from 'lucide-react';
import DataTable from '../components/DataTable';
import Modal from '../components/Modal';
import ProspectForm from '../components/ProspectForm';
import ExportImportButtons from '../components/ExportImportButtons';
import { useProspects } from '../hooks/useDatabase';
import { exportToXLS, exportToPDF, triggerFileImport } from '../utils/exportImport';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

const Prospects: React.FC = () => {
  const { isDark } = useTheme();
  const { prospects, addProspect, updateProspect, removeProspect } = useProspects();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingProspect, setEditingProspect] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const handleEmailPDF = () => {
    const email = prompt('Enter recipient email address:');
    if (email && email.includes('@')) {
      const doc = new jsPDF();
      
      // Header
      doc.setFontSize(20);
      doc.setTextColor(40, 40, 40);
      doc.text('Prospects Report', 20, 30);
      
      // Summary
      const totalProspects = filteredProspects.length;
      const wonDeals = filteredProspects.filter(p => p.status === 'Won').length;
      const lostDeals = filteredProspects.filter(p => p.status === 'Lost').length;
      const activeProspects = filteredProspects.filter(p => p.status === 'Active').length;
      
      doc.setFontSize(12);
      doc.text(`Total Prospects: ${totalProspects}`, 20, 50);
      doc.text(`Won Deals: ${wonDeals}`, 20, 65);
      doc.text(`Lost Deals: ${lostDeals}`, 20, 80);
      doc.text(`Active Prospects: ${activeProspects}`, 20, 95);
      
      // Table data
      const tableData = filteredProspects.map(prospect => [
        prospect.companyName,
        prospect.contactName,
        prospect.email,
        prospect.phone,
        prospect.status,
        prospect.dealValue || 'N/A'
      ]);
      
      (doc as any).autoTable({
        startY: 110,
        head: [['Company', 'Contact', 'Email', 'Phone', 'Status', 'Deal Value']],
        body: tableData,
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185] },
        styles: { fontSize: 8 },
        columnStyles: {
          0: { cellWidth: 30 },
          1: { cellWidth: 25 },
          2: { cellWidth: 35 },
          3: { cellWidth: 25 },
          4: { cellWidth: 20 },
          5: { cellWidth: 25 }
        }
      });
      
      // Create mailto link
      const subject = encodeURIComponent('Prospects Report');
      const body = encodeURIComponent(`Please find attached the prospects report generated on ${new Date().toLocaleDateString()}.\n\nTotal Prospects: ${totalProspects}\nWon Deals: ${wonDeals}\nLost Deals: ${lostDeals}\nActive Prospects: ${activeProspects}`);
      
      // Save PDF and open email client
      doc.save('prospects-report.pdf');
      window.open(`mailto:${email}?subject=${subject}&body=${body}`);
      
      alert('PDF downloaded and email client opened. Please attach the PDF file manually.');
    }
  };

  const filteredProspects = prospects.filter(prospect =>
    prospect.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    prospect.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    prospect.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleImport = async () => {
    try {
      const file = await triggerFileImport('.csv,.xlsx,.json');
      let data: any[] = [];
      
      if (file.name.endsWith('.csv')) {
        const text = await file.text();
        const lines = text.split('\n');
        const headers = lines[0].split(',');
        
        for (let i = 1; i < lines.length; i++) {
          if (lines[i].trim()) {
            const values = lines[i].split(',');
            const prospect: any = {};
            headers.forEach((header, index) => {
              prospect[header.trim()] = values[index]?.trim() || '';
            });
            data.push(prospect);
          }
        }
      } else if (file.name.endsWith('.json')) {
        const text = await file.text();
        data = JSON.parse(text);
      } else if (file.name.endsWith('.xlsx')) {
        // For Excel files, we'll need to use a library like xlsx
        alert('Excel import will be implemented soon');
        return;
      }
      
      if (Array.isArray(data)) {
        let importedCount = 0;
        data.forEach(prospect => {
          if (prospect.companyName && prospect.contactName) {
            addProspect({
              ...prospect,
              id: Date.now().toString() + Math.random().toString(36).substr(2, 9)
            });
            importedCount++;
          }
        });
        alert(`Successfully imported ${importedCount} prospects`);
      }
    } catch (error) {
      console.error('Import error:', error);
      alert('Error importing prospects');
    }
  };

  const handleExportExcel = () => {
    exportToXLS(filteredProspects, 'prospects');
  };

  const handleExportPDF = () => {
    const doc = new jsPDF();
    
    // Header
    doc.setFontSize(20);
    doc.setTextColor(40, 40, 40);
    doc.text('Prospects Report', 20, 30);
    
    // Summary
    const totalProspects = filteredProspects.length;
    const wonDeals = filteredProspects.filter(p => p.status === 'Won').length;
    const lostDeals = filteredProspects.filter(p => p.status === 'Lost').length;
    const activeProspects = filteredProspects.filter(p => p.status === 'Active').length;
    
    doc.setFontSize(12);
    doc.text(`Total Prospects: ${totalProspects}`, 20, 50);
    doc.text(`Won Deals: ${wonDeals}`, 20, 65);
    doc.text(`Lost Deals: ${lostDeals}`, 20, 80);
    doc.text(`Active Prospects: ${activeProspects}`, 20, 95);
    
    // Table data
    const tableData = filteredProspects.map(prospect => [
      prospect.companyName,
      prospect.contactName,
      prospect.email,
      prospect.phone,
      prospect.status,
      prospect.dealValue || 'N/A'
    ]);
    
    (doc as any).autoTable({
      startY: 110,
      head: [['Company', 'Contact', 'Email', 'Phone', 'Status', 'Deal Value']],
      body: tableData,
      theme: 'grid',
      headStyles: { fillColor: [41, 128, 185] },
      styles: { fontSize: 8 },
      columnStyles: {
        0: { cellWidth: 30 },
        1: { cellWidth: 25 },
        2: { cellWidth: 35 },
        3: { cellWidth: 25 },
        4: { cellWidth: 20 },
        5: { cellWidth: 25 }
      }
    });
    
    // Save PDF
    doc.save('prospects-report.pdf');
    
    // Ask if user wants to send via email
    const sendEmail = window.confirm('PDF saved successfully! Do you want to send it via email?');
    if (sendEmail) {
      const email = prompt('Enter recipient email address:');
      if (email && email.includes('@')) {
        // Create blob for email attachment
        const pdfBlob = doc.output('blob');
        const pdfFile = new File([pdfBlob], 'prospects-report.pdf', { type: 'application/pdf' });
        
        // Create mailto link with attachment (note: this is limited in browsers)
        const subject = encodeURIComponent('Prospects Report');
        const body = encodeURIComponent(`Please find attached the prospects report generated on ${new Date().toLocaleDateString()}.\n\nTotal Prospects: ${totalProspects}\nWon Deals: ${wonDeals}\nLost Deals: ${lostDeals}\nActive Prospects: ${activeProspects}`);
        
        // Open email client
        window.open(`mailto:${email}?subject=${subject}&body=${body}`);
        
        alert('Email client opened. Please attach the downloaded PDF file manually.');
      }
    }
  };

  const handleSave = (prospectData: any) => {
    try {
      if (editingProspect) {
        updateProspect(editingProspect.id, prospectData);
      } else {
        addProspect(prospectData);
      }
      setIsModalOpen(false);
      setEditingProspect(null);
    } catch (error) {
      console.error('Error saving prospect:', error);
      alert('Error saving prospect');
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setEditingProspect(null);
  };

  const columns = [
    { key: 'companyName', label: 'Company Name' },
    { key: 'contactName', label: 'Contact Person' },
    { key: 'email', label: 'Email' },
    { key: 'phone', label: 'Phone' },
    { key: 'source', label: 'Source' },
    { key: 'status', label: 'Status' },
    { key: 'dealValue', label: 'Deal Value' },
    { key: 'lastContact', label: 'Last Contact' },
  ];

  const handleEdit = (prospect: any) => {
    setEditingProspect(prospect);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this prospect?')) {
      removeProspect(id);
    }
  };

  const actions = [
    {
      icon: Edit,
      label: 'Edit',
      onClick: handleEdit,
      className: isDark ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-500',
    },
    {
      icon: Trash2,
      label: 'Delete',
      onClick: (item: any) => handleDelete(item.id),
      className: isDark ? 'text-red-400 hover:text-red-300' : 'text-red-600 hover:text-red-500',
    },
  ];

  const statusStats = {
    qualified: prospects.filter(p => p.status === 'Qualified').length,
    won: prospects.filter(p => p.status === 'Won').length,
    lost: prospects.filter(p => p.status === 'Lost').length,
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className={`text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>Prospect Management</h1>
          <p className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            Track and manage sales prospects and opportunities
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <ExportImportButtons
            onImport={handleImport}
            onExportExcel={handleExportExcel}
            onExportPDF={handleExportPDF}
            onEmailPDF={handleEmailPDF}
          />
          <button
            onClick={() => setIsModalOpen(true)}
            className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 transition-colors"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Prospect
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Target className="h-6 w-6 text-blue-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{statusStats.qualified}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Qualified Prospects</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Target className="h-6 w-6 text-green-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{statusStats.won}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Won Deals</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <Target className="h-6 w-6 text-red-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{statusStats.lost}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Lost Deals</div>
            </div>
          </div>
        </div>
      </div>

      <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border`}>
        <div className={`p-6 ${isDark ? 'border-b border-gray-700' : 'border-b border-gray-200'}`}>
          <div className="relative">
            <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${isDark ? 'text-gray-400' : 'text-gray-500'} h-4 w-4`} />
            <input
              type="text"
              placeholder="Search prospects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                isDark
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
              } border`}
            />
          </div>
        </div>

        <DataTable
          columns={columns}
          data={filteredProspects}
          actions={actions}
          emptyMessage="No prospects found"
        />
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingProspect(null);
        }}
        title={editingProspect ? 'Edit Prospect' : 'Add New Prospect'}
      >
        <ProspectForm
          prospect={editingProspect}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </Modal>
    </div>
  );
};

export default Prospects;