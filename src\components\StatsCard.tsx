import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { DivideIcon as LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';

interface StatsCardProps {
  name: string;
  value: string;
  icon: LucideIcon;
  change: string;
  changeType: 'increase' | 'decrease';
}

const StatsCard: React.FC<StatsCardProps> = ({
  name,
  value,
  icon: Icon,
  change,
  changeType,
}) => {
  const { isDark } = useTheme();

  return (
    <div className={`${isDark ? 'bg-gray-800 border-gray-700 hover:border-gray-600' : 'bg-white border-gray-200 hover:border-gray-300'} rounded-lg border p-6 transition-colors hover:shadow-lg`}>
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className="h-6 w-6 text-purple-400" />
        </div>
        <div className="ml-4 w-0 flex-1">
          <dl>
            <dt className={`text-sm font-medium ${isDark ? 'text-gray-400' : 'text-gray-600'} truncate`}>{name}</dt>
            <dd className="flex items-baseline">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{value}</div>
              <div
                className={`ml-2 flex items-baseline text-sm font-semibold ${
                  changeType === 'increase' ? 'text-green-400' : 'text-red-400'
                }`}
              >
                {changeType === 'increase' ? (
                  <TrendingUp className="h-4 w-4 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 mr-1" />
                )}
                {change}
              </div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  );
};

export default StatsCard;