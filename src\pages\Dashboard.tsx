import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { Users, Target, AlertCircle, DollarSign, TrendingUp, Wifi, Server } from 'lucide-react';
import StatsCard from '../components/StatsCard';
import RecentActivity from '../components/RecentActivity';
import NetworkStatus from '../components/NetworkStatus';
import Modal from '../components/Modal';
import CustomerForm from '../components/CustomerForm';
import ProspectForm from '../components/ProspectForm';
import TicketForm from '../components/TicketForm';
import InvoiceForm from '../components/InvoiceForm';
import { useCustomers, useProspects, useTickets, useInvoices, initializeDatabase } from '../hooks/useDatabase';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { isDark } = useTheme();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'customer' | 'prospect' | 'ticket' | 'invoice'>('customer');

  // Database hooks
  const { customers, addCustomer } = useCustomers();
  const { prospects, addProspect } = useProspects();
  const { tickets, addTicket } = useTickets();
  const { invoices, addInvoice } = useInvoices();

  useEffect(() => {
    // Initialize database with sample data on first load
    initializeDatabase();
  }, []);

  // Calculate dynamic stats from database
  const openTickets = tickets.filter(t => t.status === 'Open' || t.status === 'In Progress').length;
  const activeProspects = prospects.filter(p => p.status !== 'Closed' && p.status !== 'Lost').length;
  const monthlyRevenue = customers
    .filter(c => c.status === 'Active')
    .reduce((sum, c) => {
      const fee = parseInt(c.monthlyFee.replace(/[^\d]/g, '')) || 0;
      return sum + fee;
    }, 0);

  const stats = [
    { name: 'Total Customers', value: customers.length.toString(), icon: Users, change: '+4.75%', changeType: 'increase' as const, path: '/customers' },
    { name: 'Active Prospects', value: activeProspects.toString(), icon: Target, change: '+54.02%', changeType: 'increase' as const, path: '/prospects' },
    { name: 'Open Tickets', value: openTickets.toString(), icon: AlertCircle, change: '-1.39%', changeType: 'decrease' as const, path: '/customer-service' },
    { name: 'Monthly Revenue', value: `Rp ${monthlyRevenue.toLocaleString('id-ID')}`, icon: DollarSign, change: '+12.5%', changeType: 'increase' as const, path: '/invoices' },
  ];

  const openModal = (type: 'customer' | 'prospect' | 'ticket' | 'invoice') => {
    setModalType(type);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSave = (data: any) => {
    try {
      switch (modalType) {
        case 'customer':
          addCustomer(data);
          break;
        case 'prospect':
          addProspect(data);
          break;
        case 'ticket':
          addTicket(data);
          break;
        case 'invoice':
          addInvoice(data);
          break;
      }
      alert(`${modalType.charAt(0).toUpperCase() + modalType.slice(1)} saved successfully!`);
      closeModal();
    } catch (error) {
      console.error('Error saving data:', error);
      alert('Error saving data. Please try again.');
    }
  };

  const getModalTitle = () => {
    switch (modalType) {
      case 'customer': return 'Add New Customer';
      case 'prospect': return 'Add New Prospect';
      case 'ticket': return 'Create New Ticket';
      case 'invoice': return 'Generate New Invoice';
      default: return '';
    }
  };

  const renderModalContent = () => {
    switch (modalType) {
      case 'customer':
        return <CustomerForm onSave={handleSave} onCancel={closeModal} />;
      case 'prospect':
        return <ProspectForm onSave={handleSave} onCancel={closeModal} />;
      case 'ticket':
        return <TicketForm onSave={handleSave} onCancel={closeModal} />;
      case 'invoice':
        return <InvoiceForm onSave={handleSave} onCancel={closeModal} />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className={`text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>Dashboard</h1>
          <p className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            Welcome back! Here's what's happening with your ISP today.
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-950">
            <TrendingUp className="mr-2 h-4 w-4" />
            Generate Report
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} onClick={() => navigate(stat.path)} className="cursor-pointer">
            <StatsCard {...stat} />
          </div>
        ))}
      </div>

      {/* Network Status and Recent Activity */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        <NetworkStatus />
        <RecentActivity />
      </div>

      {/* Quick Actions */}
      <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border`}>
        <div className="p-6">
          <h3 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'} mb-4`}>Quick Actions</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={() => openModal('customer')}
              className={`flex items-center p-4 ${isDark ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-50 hover:bg-gray-100'} rounded-lg transition-colors`}
            >
              <Users className="h-5 w-5 text-purple-400 mr-3" />
              <span className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>Add Customer</span>
            </button>
            <button
              onClick={() => openModal('prospect')}
              className={`flex items-center p-4 ${isDark ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-50 hover:bg-gray-100'} rounded-lg transition-colors`}
            >
              <Target className="h-5 w-5 text-blue-400 mr-3" />
              <span className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>New Prospect</span>
            </button>
            <button
              onClick={() => openModal('ticket')}
              className={`flex items-center p-4 ${isDark ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-50 hover:bg-gray-100'} rounded-lg transition-colors`}
            >
              <AlertCircle className="h-5 w-5 text-yellow-400 mr-3" />
              <span className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>Create Ticket</span>
            </button>
            <button
              onClick={() => openModal('invoice')}
              className={`flex items-center p-4 ${isDark ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-50 hover:bg-gray-100'} rounded-lg transition-colors`}
            >
              <DollarSign className="h-5 w-5 text-green-400 mr-3" />
              <span className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>Generate Invoice</span>
            </button>
          </div>
        </div>
      </div>

      {/* Modal for Quick Actions */}
      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={getModalTitle()}
        size={modalType === 'invoice' ? 'xl' : 'md'}
      >
        {renderModalContent()}
      </Modal>
    </div>
  );
};

export default Dashboard;