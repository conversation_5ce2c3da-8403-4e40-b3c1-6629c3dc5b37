import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Plus, Search, Download, Upload, FileText, Eye, Send } from 'lucide-react';
import DataTable from '../components/DataTable';
import FilterBar from '../components/FilterBar';
import ExportImportButtons from '../components/ExportImportButtons';
import InvoiceDetailModal from '../components/InvoiceDetailModal';
import Modal from '../components/Modal';
import InvoiceForm from '../components/InvoiceForm';

const Invoices: React.FC = () => {
  const { isDark } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isInvoiceFormOpen, setIsInvoiceFormOpen] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState<any>(null);
  const [filters, setFilters] = useState<Record<string, string>>({
    status: '',
    month: '',
  });

  const invoices = [
    {
      id: 'INV-2024-001',
      customerName: 'PT Teknologi Maju',
      amount: 'Rp 2,500,000',
      issueDate: '2024-01-01',
      dueDate: '2024-01-31',
      status: 'Paid',
      paymentDate: '2024-01-15',
      description: 'Business Pro 100 Mbps - January 2024',
    },
    {
      id: 'INV-2024-002',
      customerName: 'CV Digital Creative',
      amount: 'Rp 1,200,000',
      issueDate: '2024-01-01',
      dueDate: '2024-01-31',
      status: 'Pending',
      paymentDate: null,
      description: 'Startup 50 Mbps - January 2024',
    },
    {
      id: 'INV-2024-003',
      customerName: 'PT Solusi Digital',
      amount: 'Rp 8,500,000',
      issueDate: '2024-01-01',
      dueDate: '2024-01-31',
      status: 'Overdue',
      paymentDate: null,
      description: 'Enterprise 500 Mbps - January 2024',
    },
  ];

  const columns = [
    { key: 'id', label: 'Invoice ID' },
    { key: 'customerName', label: 'Customer' },
    { key: 'amount', label: 'Amount' },
    { key: 'issueDate', label: 'Issue Date' },
    { key: 'dueDate', label: 'Due Date' },
    { key: 'status', label: 'Status' },
    { key: 'description', label: 'Description' },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: ['Paid', 'Pending', 'Overdue'],
    },
    {
      key: 'month',
      label: 'Month',
      options: ['January', 'February', 'March', 'April', 'May', 'June'],
    },
  ];

  const handleViewInvoice = (invoice: any) => {
    setSelectedInvoice(invoice);
    setIsDetailModalOpen(true);
  };

  const handleSendEmail = (invoice: any) => {
    console.log('Sending email for invoice:', invoice.id);
    // Implement email sending functionality
    alert(`Email sent for invoice ${invoice.id}`);
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleClearFilters = () => {
    setFilters({ status: '', month: '' });
  };

  const handleImport = () => {
    console.log('Import invoices');
  };

  const handleExportExcel = () => {
    console.log('Export invoices to Excel');
  };

  const handleExportPDF = () => {
    console.log('Export invoices to PDF');
  };

  const handleSaveInvoice = (data: any) => {
    console.log('Save invoice:', data);
    // Here you would typically save the invoice to your backend
    alert('Invoice generated successfully!');
    setIsInvoiceFormOpen(false);
    setEditingInvoice(null);
  };

  const handleCancelInvoice = () => {
    setIsInvoiceFormOpen(false);
    setEditingInvoice(null);
  };

  const actions = [
    {
      icon: Eye,
      label: 'View',
      onClick: handleViewInvoice,
      className: 'text-blue-400 hover:text-blue-300',
    },
    {
      icon: Send,
      label: 'Send',
      onClick: handleSendEmail,
      className: 'text-green-400 hover:text-green-300',
    },
    {
      icon: Download,
      label: 'Download',
      onClick: (invoice: any) => console.log('Download invoice:', invoice),
      className: 'text-purple-400 hover:text-purple-300',
    },
  ];

  const invoiceStats = {
    total: invoices.length,
    paid: invoices.filter(i => i.status === 'Paid').length,
    pending: invoices.filter(i => i.status === 'Pending').length,
    overdue: invoices.filter(i => i.status === 'Overdue').length,
    totalAmount: invoices.reduce((sum, invoice) => {
      const amount = parseInt(invoice.amount.replace(/[^0-9]/g, ''));
      return sum + amount;
    }, 0),
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !filters.status || invoice.status === filters.status;
    const matchesMonth = !filters.month || invoice.description.includes(filters.month);
    
    return matchesSearch && matchesStatus && matchesMonth;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className={`text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>Invoice Management</h1>
          <p className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            Manage customer billing and invoice generation
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <ExportImportButtons
            onImport={handleImport}
            onExportExcel={handleExportExcel}
            onExportPDF={handleExportPDF}
          />
          <button
            onClick={() => setIsInvoiceFormOpen(true)}
            className="inline-flex items-center rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700"
          >
            <Plus className="mr-2 h-4 w-4" />
            Generate Invoice
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-4">
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <FileText className="h-6 w-6 text-blue-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{invoiceStats.total}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Total Invoices</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <FileText className="h-6 w-6 text-green-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{invoiceStats.paid}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Paid</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <FileText className="h-6 w-6 text-yellow-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{invoiceStats.pending}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Pending</div>
            </div>
          </div>
        </div>
        <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
          <div className="flex items-center">
            <FileText className="h-6 w-6 text-red-400" />
            <div className="ml-4">
              <div className={`text-2xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>{invoiceStats.overdue}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>Overdue</div>
            </div>
          </div>
        </div>
      </div>

      <div className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border`}>
        <div className={`p-6 border-b ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="relative">
            <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${isDark ? 'text-gray-400' : 'text-gray-500'} h-4 w-4`} />
            <input
              type="text"
              placeholder="Search invoices..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'} border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
            />
          </div>
        </div>

        <FilterBar
          filters={filterOptions}
          activeFilters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
        />

        <DataTable
          columns={columns}
          data={filteredInvoices}
          actions={actions}
          emptyMessage="No invoices found"
        />
      </div>

      <InvoiceDetailModal
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        invoice={selectedInvoice}
        onSendEmail={handleSendEmail}
      />

      {/* Modal for Generate Invoice */}
      <Modal
        isOpen={isInvoiceFormOpen}
        onClose={handleCancelInvoice}
        title={editingInvoice ? 'Edit Invoice' : 'Generate New Invoice'}
        size="xl"
      >
        <InvoiceForm
          invoice={editingInvoice}
          onSave={handleSaveInvoice}
          onCancel={handleCancelInvoice}
        />
      </Modal>
    </div>
  );
};

export default Invoices;