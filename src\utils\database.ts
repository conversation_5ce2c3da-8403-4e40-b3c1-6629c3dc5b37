// Local Database using localStorage
export interface Customer {
  id: string;
  companyName: string;
  picName: string;
  picPhone: string;
  picTechnical?: string;
  picTechnicalPhone?: string;
  picTechnicalEmail?: string;
  email: string;
  phone: string;
  customPackage: string;
  status: 'Active' | 'Inactive' | 'Suspended' | 'Terminated';
  monthlyFee: string;
  address: string;
  latitude?: string;
  longitude?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Prospect {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  status: string;
  source: string;
  interestedPackage: string;
  estimatedValue: string;
  followUpDate: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Ticket {
  id: string;
  customer: string;
  subject: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  category: string;
  assignedTo?: string;
  status: 'Open' | 'In Progress' | 'Resolved' | 'Closed';
  createdDate: string;
  lastUpdate: string;
  startTrouble?: string;
  finishedTrouble?: string;
  duration?: string;
  rfo?: string;
}

export interface Invoice {
  id: string;
  customerName: string;
  customerEmail?: string;
  customerAddress?: string;
  issueDate: string;
  dueDate: string;
  description?: string;
  items: InvoiceItem[];
  totalAmount: number;
  amount: string;
  status: 'Pending' | 'Paid' | 'Overdue' | 'Cancelled';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

class LocalDatabase {
  private getStorageKey(table: string): string {
    return `crm_wiznet_${table}`;
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  // Generic CRUD operations
  private getAll<T>(table: string): T[] {
    const data = localStorage.getItem(this.getStorageKey(table));
    return data ? JSON.parse(data) : [];
  }

  private save<T>(table: string, data: T[]): void {
    localStorage.setItem(this.getStorageKey(table), JSON.stringify(data));
  }

  private create<T extends { id: string; createdAt: string; updatedAt: string }>(
    table: string, 
    item: Omit<T, 'id' | 'createdAt' | 'updatedAt'>
  ): T {
    const items = this.getAll<T>(table);
    const now = this.formatDate(new Date());
    const newItem = {
      ...item,
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
    } as T;
    
    items.push(newItem);
    this.save(table, items);
    return newItem;
  }

  private update<T extends { id: string; updatedAt: string }>(
    table: string, 
    id: string, 
    updates: Partial<Omit<T, 'id' | 'createdAt'>>
  ): T | null {
    const items = this.getAll<T>(table);
    const index = items.findIndex(item => item.id === id);
    
    if (index === -1) return null;
    
    const updatedItem = {
      ...items[index],
      ...updates,
      updatedAt: this.formatDate(new Date()),
    };
    
    items[index] = updatedItem;
    this.save(table, items);
    return updatedItem;
  }

  private delete<T extends { id: string }>(table: string, id: string): boolean {
    const items = this.getAll<T>(table);
    const filteredItems = items.filter(item => item.id !== id);
    
    if (filteredItems.length === items.length) return false;
    
    this.save(table, filteredItems);
    return true;
  }

  // Customer operations
  getCustomers(): Customer[] {
    return this.getAll<Customer>('customers');
  }

  createCustomer(customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Customer {
    return this.create<Customer>('customers', customer);
  }

  updateCustomer(id: string, updates: Partial<Omit<Customer, 'id' | 'createdAt'>>): Customer | null {
    return this.update<Customer>('customers', id, updates);
  }

  deleteCustomer(id: string): boolean {
    return this.delete<Customer>('customers', id);
  }

  // Prospect operations
  getProspects(): Prospect[] {
    return this.getAll<Prospect>('prospects');
  }

  createProspect(prospect: Omit<Prospect, 'id' | 'createdAt' | 'updatedAt'>): Prospect {
    return this.create<Prospect>('prospects', prospect);
  }

  updateProspect(id: string, updates: Partial<Omit<Prospect, 'id' | 'createdAt'>>): Prospect | null {
    return this.update<Prospect>('prospects', id, updates);
  }

  deleteProspect(id: string): boolean {
    return this.delete<Prospect>('prospects', id);
  }

  // Ticket operations
  getTickets(): Ticket[] {
    return this.getAll<Ticket>('tickets');
  }

  createTicket(ticket: Omit<Ticket, 'id' | 'createdDate' | 'lastUpdate'>): Ticket {
    const tickets = this.getAll<Ticket>('tickets');
    const now = this.formatDate(new Date());
    const newTicket = {
      ...ticket,
      id: `TKT-${Date.now()}`,
      createdDate: now,
      lastUpdate: now,
    } as Ticket;
    
    tickets.push(newTicket);
    this.save('tickets', tickets);
    return newTicket;
  }

  updateTicket(id: string, updates: Partial<Omit<Ticket, 'id' | 'createdDate'>>): Ticket | null {
    const tickets = this.getAll<Ticket>('tickets');
    const index = tickets.findIndex(ticket => ticket.id === id);
    
    if (index === -1) return null;
    
    const updatedTicket = {
      ...tickets[index],
      ...updates,
      lastUpdate: this.formatDate(new Date()),
    };
    
    // Calculate duration if both start and finish times are provided
    if (updatedTicket.startTrouble && updatedTicket.finishedTrouble) {
      const start = new Date(updatedTicket.startTrouble);
      const finish = new Date(updatedTicket.finishedTrouble);
      const diffMs = finish.getTime() - start.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      updatedTicket.duration = `${diffHours}h ${diffMinutes}m`;
    }
    
    tickets[index] = updatedTicket;
    this.save('tickets', tickets);
    return updatedTicket;
  }

  deleteTicket(id: string): boolean {
    return this.delete<Ticket>('tickets', id);
  }

  // Invoice operations
  getInvoices(): Invoice[] {
    return this.getAll<Invoice>('invoices');
  }

  createInvoice(invoice: Omit<Invoice, 'id' | 'createdAt' | 'updatedAt'>): Invoice {
    return this.create<Invoice>('invoices', invoice);
  }

  updateInvoice(id: string, updates: Partial<Omit<Invoice, 'id' | 'createdAt'>>): Invoice | null {
    return this.update<Invoice>('invoices', id, updates);
  }

  deleteInvoice(id: string): boolean {
    return this.delete<Invoice>('invoices', id);
  }

  // Initialize with sample data if empty
  initializeSampleData(): void {
    if (this.getCustomers().length === 0) {
      // Add sample customers
      this.createCustomer({
        companyName: 'PT. Teknologi Maju',
        picName: 'John Doe',
        picPhone: '***********',
        email: '<EMAIL>',
        phone: '021-12345678',
        customPackage: 'Business Pro 100 Mbps',
        status: 'Active',
        monthlyFee: '2,500,000',
        address: 'Jl. Sudirman No. 123, Jakarta Pusat 10220',
        latitude: '-6.2088',
        longitude: '106.8456',
      });
    }
  }
}

export const db = new LocalDatabase();
