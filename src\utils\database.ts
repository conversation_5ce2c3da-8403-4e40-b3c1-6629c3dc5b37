// Local Database using localStorage
export interface Customer {
  id: string;
  companyName: string;
  picName: string;
  picPhone: string;
  picTechnical?: string;
  picTechnicalPhone?: string;
  picTechnicalEmail?: string;
  email: string;
  phone: string;
  customPackage: string;
  status: 'Active' | 'Inactive' | 'Suspended' | 'Terminated';
  monthlyFee: string;
  address: string;
  latitude?: string;
  longitude?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Prospect {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  status: string;
  source: string;
  interestedPackage: string;
  estimatedValue: string;
  followUpDate: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Ticket {
  id: string;
  customer: string;
  subject: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  category: string;
  assignedTo?: string;
  status: 'Open' | 'In Progress' | 'Resolved' | 'Closed';
  createdDate: string;
  lastUpdate: string;
  startTrouble?: string;
  finishedTrouble?: string;
  duration?: string;
  rfo?: string;
}

export interface Invoice {
  id: string;
  customerName: string;
  customerEmail?: string;
  customerAddress?: string;
  issueDate: string;
  dueDate: string;
  description?: string;
  items: InvoiceItem[];
  totalAmount: number;
  amount: string;
  status: 'Pending' | 'Paid' | 'Overdue' | 'Cancelled';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

class LocalDatabase {
  private getStorageKey(table: string): string {
    return `crm_wiznet_${table}`;
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  // Generic CRUD operations
  private getAll<T>(table: string): T[] {
    const data = localStorage.getItem(this.getStorageKey(table));
    return data ? JSON.parse(data) : [];
  }

  private save<T>(table: string, data: T[]): void {
    localStorage.setItem(this.getStorageKey(table), JSON.stringify(data));
  }

  private create<T extends { id: string; createdAt: string; updatedAt: string }>(
    table: string, 
    item: Omit<T, 'id' | 'createdAt' | 'updatedAt'>
  ): T {
    const items = this.getAll<T>(table);
    const now = this.formatDate(new Date());
    const newItem = {
      ...item,
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
    } as T;
    
    items.push(newItem);
    this.save(table, items);
    return newItem;
  }

  private update<T extends { id: string; updatedAt: string }>(
    table: string, 
    id: string, 
    updates: Partial<Omit<T, 'id' | 'createdAt'>>
  ): T | null {
    const items = this.getAll<T>(table);
    const index = items.findIndex(item => item.id === id);
    
    if (index === -1) return null;
    
    const updatedItem = {
      ...items[index],
      ...updates,
      updatedAt: this.formatDate(new Date()),
    };
    
    items[index] = updatedItem;
    this.save(table, items);
    return updatedItem;
  }

  private delete<T extends { id: string }>(table: string, id: string): boolean {
    const items = this.getAll<T>(table);
    const filteredItems = items.filter(item => item.id !== id);
    
    if (filteredItems.length === items.length) return false;
    
    this.save(table, filteredItems);
    return true;
  }

  // Customer operations
  getCustomers(): Customer[] {
    return this.getAll<Customer>('customers');
  }

  createCustomer(customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Customer {
    return this.create<Customer>('customers', customer);
  }

  updateCustomer(id: string, updates: Partial<Omit<Customer, 'id' | 'createdAt'>>): Customer | null {
    return this.update<Customer>('customers', id, updates);
  }

  deleteCustomer(id: string): boolean {
    return this.delete<Customer>('customers', id);
  }

  // Prospect operations
  getProspects(): Prospect[] {
    return this.getAll<Prospect>('prospects');
  }

  createProspect(prospect: Omit<Prospect, 'id' | 'createdAt' | 'updatedAt'>): Prospect {
    return this.create<Prospect>('prospects', prospect);
  }

  updateProspect(id: string, updates: Partial<Omit<Prospect, 'id' | 'createdAt'>>): Prospect | null {
    return this.update<Prospect>('prospects', id, updates);
  }

  deleteProspect(id: string): boolean {
    return this.delete<Prospect>('prospects', id);
  }

  // Ticket operations
  getTickets(): Ticket[] {
    return this.getAll<Ticket>('tickets');
  }

  createTicket(ticket: Omit<Ticket, 'id' | 'createdDate' | 'lastUpdate'>): Ticket {
    const tickets = this.getAll<Ticket>('tickets');
    const now = this.formatDate(new Date());
    const newTicket = {
      ...ticket,
      id: `TKT-${Date.now()}`,
      createdDate: now,
      lastUpdate: now,
    } as Ticket;
    
    tickets.push(newTicket);
    this.save('tickets', tickets);
    return newTicket;
  }

  updateTicket(id: string, updates: Partial<Omit<Ticket, 'id' | 'createdDate'>>): Ticket | null {
    const tickets = this.getAll<Ticket>('tickets');
    const index = tickets.findIndex(ticket => ticket.id === id);
    
    if (index === -1) return null;
    
    const updatedTicket = {
      ...tickets[index],
      ...updates,
      lastUpdate: this.formatDate(new Date()),
    };
    
    // Calculate duration if both start and finish times are provided
    if (updatedTicket.startTrouble && updatedTicket.finishedTrouble) {
      const start = new Date(updatedTicket.startTrouble);
      const finish = new Date(updatedTicket.finishedTrouble);
      const diffMs = finish.getTime() - start.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      updatedTicket.duration = `${diffHours}h ${diffMinutes}m`;
    }
    
    tickets[index] = updatedTicket;
    this.save('tickets', tickets);
    return updatedTicket;
  }

  deleteTicket(id: string): boolean {
    return this.delete<Ticket>('tickets', id);
  }

  // Invoice operations
  getInvoices(): Invoice[] {
    return this.getAll<Invoice>('invoices');
  }

  createInvoice(invoice: Omit<Invoice, 'id' | 'createdAt' | 'updatedAt'>): Invoice {
    return this.create<Invoice>('invoices', invoice);
  }

  updateInvoice(id: string, updates: Partial<Omit<Invoice, 'id' | 'createdAt'>>): Invoice | null {
    return this.update<Invoice>('invoices', id, updates);
  }

  deleteInvoice(id: string): boolean {
    return this.delete<Invoice>('invoices', id);
  }

  // Clear all data (for development/testing)
  clearAllData(): void {
    localStorage.removeItem(this.getStorageKey('customers'));
    localStorage.removeItem(this.getStorageKey('prospects'));
    localStorage.removeItem(this.getStorageKey('tickets'));
    localStorage.removeItem(this.getStorageKey('invoices'));
  }

  // Initialize with sample data if empty
  initializeSampleData(): void {
    // Force clear and reinitialize to ensure fresh data
    this.clearAllData();
    
    // Add sample customers
    this.createCustomer({
        companyName: 'PT. Teknologi Maju',
        picName: 'John Doe',
        picPhone: '***********',
        picTechnical: 'Jane Smith',
        picTechnicalPhone: '***********',
        picTechnicalEmail: '<EMAIL>',
        email: '<EMAIL>',
        phone: '021-12345678',
        customPackage: 'Business Pro 100 Mbps',
        status: 'Active',
        monthlyFee: 'Rp 2,500,000',
        address: 'Jl. Sudirman No. 123, Jakarta Pusat 10220',
        latitude: '-6.2088',
        longitude: '106.8456',
        notes: 'Premium customer with excellent payment history'
      });

      this.createCustomer({
        companyName: 'CV. Digital Solutions',
        picName: 'Ahmad Rahman',
        picPhone: '***********',
        picTechnical: 'Siti Nurhaliza',
        picTechnicalPhone: '***********',
        picTechnicalEmail: '<EMAIL>',
        email: '<EMAIL>',
        phone: '021-87654321',
        customPackage: 'Startup 50 Mbps',
        status: 'Active',
        monthlyFee: 'Rp 1,200,000',
        address: 'Jl. Gatot Subroto No. 45, Jakarta Selatan 12190',
        latitude: '-6.2297',
        longitude: '106.8253',
        notes: 'Growing startup company, potential for upgrade'
      });

      this.createCustomer({
        companyName: 'PT. Mandiri Sejahtera',
        picName: 'Budi Santoso',
        picPhone: '08345678901',
        email: '<EMAIL>',
        phone: '021-11223344',
        customPackage: 'Enterprise 500 Mbps',
        status: 'Active',
        monthlyFee: 'Rp 8,500,000',
        address: 'Jl. Thamrin No. 78, Jakarta Pusat 10310',
        latitude: '-6.1944',
        longitude: '106.8229',
        notes: 'Large enterprise client with multiple locations'
      });

      this.createCustomer({
        companyName: 'Toko Berkah Jaya',
        picName: 'Ibu Sari',
        picPhone: '08456789012',
        email: '<EMAIL>',
        phone: '021-55667788',
        customPackage: 'Basic 25 Mbps',
        status: 'Suspended',
        monthlyFee: 'Rp 750,000',
        address: 'Jl. Raya Bekasi No. 234, Bekasi 17141',
        latitude: '-6.2383',
        longitude: '106.9756',
        notes: 'Payment overdue, suspended since last month'
      });

      this.createCustomer({
        companyName: 'PT. Inovasi Digital',
        picName: 'Dr. Andi Wijaya',
        picPhone: '08567890123',
        picTechnical: 'Rizki Pratama',
        picTechnicalPhone: '08567890124',
        picTechnicalEmail: '<EMAIL>',
        email: '<EMAIL>',
        phone: '021-99887766',
        customPackage: 'Premium 1 Gbps',
        status: 'Active',
        monthlyFee: 'Rp 15,000,000',
        address: 'Jl. Kuningan Raya No. 12, Jakarta Selatan 12940',
        latitude: '-6.2297',
        longitude: '106.8253',
        notes: 'High-tech company requiring ultra-fast internet'
      });

    // Add sample prospects
    this.createProspect({
      companyName: 'PT. Masa Depan Tech',
      contactPerson: 'Lisa Permata',
      email: '<EMAIL>',
      phone: '***********',
      status: 'Hot Lead',
      source: 'Website',
      interestedPackage: 'Business Pro 200 Mbps',
      estimatedValue: 'Rp 4,500,000',
      followUpDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      notes: 'Very interested, scheduled demo next week'
    });

    this.createProspect({
      companyName: 'CV. Kreatif Media',
      contactPerson: 'Tommy Setiawan',
      email: '<EMAIL>',
      phone: '***********',
      status: 'Warm Lead',
      source: 'Referral',
      interestedPackage: 'Startup 75 Mbps',
      estimatedValue: 'Rp 1,800,000',
      followUpDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      notes: 'Referred by existing customer, price sensitive'
    });

    // Add sample tickets
    this.createTicket({
      customer: 'PT. Teknologi Maju',
      subject: 'Koneksi Internet Lambat',
      description: 'Kecepatan internet menurun drastis sejak kemarin, mohon bantuan untuk pengecekan',
      priority: 'High',
      category: 'Technical',
      assignedTo: 'Tim Teknis A',
      status: 'In Progress',
      startTrouble: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      rfo: 'Sedang dilakukan pengecekan pada perangkat router'
    });

    this.createTicket({
      customer: 'CV. Digital Solutions',
      subject: 'Request Upgrade Bandwidth',
      description: 'Permintaan upgrade dari paket 50 Mbps ke 100 Mbps untuk mendukung operasional yang semakin besar',
      priority: 'Medium',
      category: 'Sales',
      assignedTo: 'Tim Sales',
      status: 'Open',
      rfo: 'Menunggu konfirmasi dari customer untuk jadwal upgrade'
    });

    this.createTicket({
      customer: 'Toko Berkah Jaya',
      subject: 'Pembayaran Tertunggak',
      description: 'Tagihan bulan lalu belum dibayar, layanan akan disuspend jika tidak ada pembayaran dalam 3 hari',
      priority: 'Critical',
      category: 'Billing',
      assignedTo: 'Tim Finance',
      status: 'Open',
      rfo: 'Menunggu pembayaran dari customer'
    });

     // Add sample invoices
    this.createInvoice({
      customerName: 'PT. Teknologi Maju',
      customerEmail: '<EMAIL>',
      customerAddress: 'Jl. Sudirman No. 123, Jakarta Pusat 10220',
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      description: 'Tagihan bulanan layanan internet Business Pro 100 Mbps',
      items: [
        {
          description: 'Business Pro 100 Mbps - Januari 2024',
          quantity: 1,
          unitPrice: 2500000,
          total: 2500000
        },
        {
          description: 'Biaya Administrasi',
          quantity: 1,
          unitPrice: 50000,
          total: 50000
        }
      ],
      totalAmount: 2550000,
      amount: 'Rp 2,550,000',
      status: 'Paid',
      notes: 'Pembayaran diterima tepat waktu'
    });

    this.createInvoice({
      customerName: 'CV. Digital Solutions',
      customerEmail: '<EMAIL>',
      customerAddress: 'Jl. Gatot Subroto No. 45, Jakarta Selatan 12190',
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      description: 'Tagihan bulanan layanan internet Startup 50 Mbps',
      items: [
        {
          description: 'Startup 50 Mbps - Januari 2024',
          quantity: 1,
          unitPrice: 1200000,
          total: 1200000
        }
      ],
      totalAmount: 1200000,
      amount: 'Rp 1,200,000',
      status: 'Pending',
      notes: 'Menunggu pembayaran'
    });

    this.createInvoice({
      customerName: 'Toko Berkah Jaya',
      customerEmail: '<EMAIL>',
      customerAddress: 'Jl. Raya Bekasi No. 234, Bekasi 17141',
      issueDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      dueDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      description: 'Tagihan bulanan layanan internet Basic 25 Mbps',
      items: [
        {
          description: 'Basic 25 Mbps - Desember 2023',
          quantity: 1,
          unitPrice: 750000,
          total: 750000
        }
      ],
      totalAmount: 750000,
      amount: 'Rp 750,000',
      status: 'Overdue',
      notes: 'Tagihan sudah jatuh tempo, layanan disuspend'
    });
  }
}

export const db = new LocalDatabase();
