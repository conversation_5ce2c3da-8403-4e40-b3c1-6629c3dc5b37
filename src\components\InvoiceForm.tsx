import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Plus, Trash2 } from 'lucide-react';

interface InvoiceFormProps {
  invoice?: any;
  onSave: (data: any) => void;
  onCancel: () => void;
}

interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

const InvoiceForm: React.FC<InvoiceFormProps> = ({ invoice, onSave, onCancel }) => {
  const { isDark } = useTheme();
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerAddress: '',
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: '',
    description: '',
    notes: '',
  });

  const [items, setItems] = useState<InvoiceItem[]>([
    { description: '', quantity: 1, unitPrice: 0, total: 0 }
  ]);

  useEffect(() => {
    if (invoice) {
      setFormData({
        customerName: invoice.customerName || '',
        customerEmail: invoice.customerEmail || '',
        customerAddress: invoice.customerAddress || '',
        issueDate: invoice.issueDate || new Date().toISOString().split('T')[0],
        dueDate: invoice.dueDate || '',
        description: invoice.description || '',
        notes: invoice.notes || '',
      });
      if (invoice.items && invoice.items.length > 0) {
        setItems(invoice.items);
      }
    }
  }, [invoice]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const totalAmount = items.reduce((sum, item) => sum + item.total, 0);
    const invoiceData = {
      ...formData,
      items,
      id: invoice?.id || `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`,
      amount: `Rp ${totalAmount.toLocaleString('id-ID')}`,
      status: invoice?.status || 'Pending',
      totalAmount,
    };
    onSave(invoiceData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleItemChange = (index: number, field: keyof InvoiceItem, value: string | number) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };
    
    // Calculate total for this item
    if (field === 'quantity' || field === 'unitPrice') {
      newItems[index].total = newItems[index].quantity * newItems[index].unitPrice;
    }
    
    setItems(newItems);
  };

  const addItem = () => {
    setItems([...items, { description: '', quantity: 1, unitPrice: 0, total: 0 }]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  const totalAmount = items.reduce((sum, item) => sum + item.total, 0);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Customer Information */}
      <div className="space-y-4">
        <h4 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>Customer Information</h4>
        
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="customerName" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Customer Name *
            </label>
            <input
              type="text"
              id="customerName"
              name="customerName"
              required
              value={formData.customerName}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
            />
          </div>

          <div>
            <label htmlFor="customerEmail" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Customer Email
            </label>
            <input
              type="email"
              id="customerEmail"
              name="customerEmail"
              value={formData.customerEmail}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
            />
          </div>
        </div>

        <div>
          <label htmlFor="customerAddress" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Customer Address
          </label>
          <textarea
            id="customerAddress"
            name="customerAddress"
            rows={2}
            value={formData.customerAddress}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
      </div>

      {/* Invoice Details */}
      <div className="space-y-4">
        <h4 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>Invoice Details</h4>
        
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="issueDate" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Issue Date *
            </label>
            <input
              type="date"
              id="issueDate"
              name="issueDate"
              required
              value={formData.issueDate}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
            />
          </div>

          <div>
            <label htmlFor="dueDate" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Due Date *
            </label>
            <input
              type="date"
              id="dueDate"
              name="dueDate"
              required
              value={formData.dueDate}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
            />
          </div>
        </div>

        <div>
          <label htmlFor="description" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Description
          </label>
          <input
            type="text"
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="e.g., Business Pro 100 Mbps - January 2024"
            className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
          />
        </div>
      </div>

      {/* Invoice Items */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>Invoice Items</h4>
          <button
            type="button"
            onClick={addItem}
            className="inline-flex items-center px-3 py-1 text-sm font-medium text-purple-400 hover:text-purple-300"
          >
            <Plus className="mr-1 h-4 w-4" />
            Add Item
          </button>
        </div>

        <div className="space-y-3">
          {items.map((item, index) => (
            <div key={index} className={`p-4 rounded-lg border ${isDark ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
              <div className="grid grid-cols-1 gap-3 sm:grid-cols-12 items-end">
                <div className="sm:col-span-5">
                  <label className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                    Description
                  </label>
                  <input
                    type="text"
                    value={item.description}
                    onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                    className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
                  />
                </div>
                <div className="sm:col-span-2">
                  <label className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                    Qty
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={item.quantity}
                    onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 1)}
                    className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
                  />
                </div>
                <div className="sm:col-span-2">
                  <label className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                    Unit Price
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={item.unitPrice}
                    onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                    className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300 text-gray-900'} focus:border-purple-500 focus:ring-purple-500`}
                  />
                </div>
                <div className="sm:col-span-2">
                  <label className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                    Total
                  </label>
                  <div className={`mt-1 px-3 py-2 rounded-md ${isDark ? 'bg-gray-600 text-gray-300' : 'bg-gray-100 text-gray-700'}`}>
                    Rp {item.total.toLocaleString('id-ID')}
                  </div>
                </div>
                <div className="sm:col-span-1">
                  {items.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeItem(index)}
                      className="p-2 text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className={`p-4 rounded-lg ${isDark ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <div className="flex justify-between items-center">
            <span className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Total Amount:
            </span>
            <span className={`text-xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Rp {totalAmount.toLocaleString('id-ID')}
            </span>
          </div>
        </div>
      </div>

      {/* Notes */}
      <div>
        <label htmlFor="notes" className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
          Notes
        </label>
        <textarea
          id="notes"
          name="notes"
          rows={3}
          value={formData.notes}
          onChange={handleChange}
          placeholder="Additional notes or terms"
          className={`mt-1 block w-full rounded-md ${isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:border-purple-500 focus:ring-purple-500`}
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            isDark 
              ? 'text-gray-300 bg-gray-700 border-gray-600 hover:bg-gray-600' 
              : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
          } border focus:outline-none focus:ring-2 focus:ring-purple-500`}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          {invoice ? 'Update Invoice' : 'Generate Invoice'}
        </button>
      </div>
    </form>
  );
};

export default InvoiceForm;
